#!/usr/bin/env python3
"""
Comprehensive test script for BudgetBot functionality.

This script verifies that BudgetBot has been successfully restored and integrated with:
1. Simplified order summary embed format
2. Current tracking system with persistent embeds
3. Automatic delivered order cleanup
4. Budget-specific branding and configurations
5. All essential commands and functionality
"""

import sys
import os
import re
import importlib.util

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_budgetbot_structure():
    """Test that BudgetBot directory structure exists."""
    print("🧪 Testing BudgetBot Directory Structure")
    print("=" * 50)
    
    required_files = [
        'budgetbot/__init__.py',
        'budgetbot/budgetbot.py',
        'budgetbot/embed_templates.py',
        'budgetbot/config.py'
    ]
    
    structure_success = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ Found: {file_path}")
        else:
            print(f"   ❌ Missing: {file_path}")
            structure_success = False
    
    if structure_success:
        print("   ✅ SUCCESS: BudgetBot directory structure complete")
    else:
        print("   ❌ FAILURE: BudgetBot directory structure incomplete")
        return False
    
    return True

def test_budgetbot_imports():
    """Test that BudgetBot modules can be imported."""
    print("\n🧪 Testing BudgetBot Module Imports")
    print("=" * 50)
    
    import_tests = [
        ('budgetbot.config', 'BudgetBot configuration'),
        ('budgetbot.embed_templates', 'BudgetBot embed templates'),
        ('budgetbot.budgetbot', 'BudgetBot main module')
    ]
    
    import_success = True
    for module_name, description in import_tests:
        try:
            spec = importlib.util.find_spec(module_name)
            if spec is not None:
                print(f"   ✅ Can import: {description}")
            else:
                print(f"   ❌ Cannot find: {description}")
                import_success = False
        except Exception as e:
            print(f"   ❌ Import error for {description}: {e}")
            import_success = False
    
    if import_success:
        print("   ✅ SUCCESS: All BudgetBot modules can be imported")
    else:
        print("   ❌ FAILURE: Some BudgetBot modules cannot be imported")
        return False
    
    return True

def test_budgetbot_configuration():
    """Test BudgetBot configuration and settings."""
    print("\n🧪 Testing BudgetBot Configuration")
    print("=" * 50)
    
    try:
        with open('budgetbot/config.py', 'r', encoding='utf-8') as f:
            config_content = f.read()
    except FileNotFoundError:
        print("   ❌ FAILURE: budgetbot/config.py not found")
        return False
    
    # Test configuration components
    config_patterns = [
        r'BUDGETBOT_NAME = "BudgetBot"',
        r'BUDGETBOT_COLORS = \{',
        r'BUDGETBOT_PRICING = \{',
        r'min_subtotal_usd.*20\.0',
        r'max_subtotal_usd.*35\.0',
        r'fixed_discount_usd.*25\.00',
        r'method_fee_usd.*10\.00',
        r'BUDGETBOT_MESSAGES = \{',
        r'get_budgetbot_config\(\)',
        r'validate_budgetbot_config\(\)'
    ]
    
    config_success = True
    for pattern in config_patterns:
        if re.search(pattern, config_content):
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            config_success = False
    
    if config_success:
        print("   ✅ SUCCESS: BudgetBot configuration properly implemented")
    else:
        print("   ❌ FAILURE: BudgetBot configuration incomplete")
        return False
    
    return True

def test_budgetbot_embed_templates():
    """Test BudgetBot embed templates with simplified format."""
    print("\n🧪 Testing BudgetBot Embed Templates")
    print("=" * 50)
    
    try:
        with open('budgetbot/embed_templates.py', 'r', encoding='utf-8') as f:
            embed_content = f.read()
    except FileNotFoundError:
        print("   ❌ FAILURE: budgetbot/embed_templates.py not found")
        return False
    
    # Test simplified embed format
    embed_patterns = [
        r'create_budgetbot_order_summary_embed',
        r'create_budgetbot_latestsummary_embed',
        r'create_budgetbot_error_embed',
        r'create_budgetbot_processing_embed',
        r'BUDGETBOT_PRIMARY_COLOR.*255, 165, 0',
        r'title="🍔 BudgetBot Order Summary"',
        r'Delivery Location',
        r'Restaurant',
        r'Order Items',
        r'Subtotal',
        r'BudgetBot \| Budget-Friendly Order Summary'
    ]
    
    embed_success = True
    for pattern in embed_patterns:
        if re.search(pattern, embed_content):
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            embed_success = False
    
    # Test that complex pricing sections are NOT present
    removed_patterns = [
        r'Price Breakdown.*Original Subtotal.*Discounted',
        r'Fees & Taxes',
        r'Estimated Final Total',
        r'final_total',
        r'before_amount',
        r'total_savings'
    ]
    
    for pattern in removed_patterns:
        if re.search(pattern, embed_content):
            print(f"   ❌ Found (should be removed): {pattern}")
            embed_success = False
        else:
            print(f"   ✅ Removed: {pattern}")
    
    if embed_success:
        print("   ✅ SUCCESS: BudgetBot embed templates properly simplified")
    else:
        print("   ❌ FAILURE: BudgetBot embed templates incomplete")
        return False
    
    return True

def test_budgetbot_main_implementation():
    """Test BudgetBot main implementation."""
    print("\n🧪 Testing BudgetBot Main Implementation")
    print("=" * 50)
    
    try:
        with open('budgetbot/budgetbot.py', 'r', encoding='utf-8') as f:
            budgetbot_content = f.read()
    except FileNotFoundError:
        print("   ❌ FAILURE: budgetbot/budgetbot.py not found")
        return False
    
    # Test main implementation components
    main_patterns = [
        r'BUDGET_DISCORD_BOT_TOKEN',
        r'BUDGET_DISCORD_GUILD_ID',
        r'bot = commands\.Bot\(',
        r'command_prefix="!budget"',
        r'track_command_metrics',
        r'save_tracking_data',
        r'load_tracking_data',
        r'name="check"',
        r'name="latestsummary"',
        r'name="track"',
        r'name="open"',
        r'name="close"',
        r'bot_name="budgetbot"',
        r'async def setup_hook',
        r'async def on_ready',
        r'def run_bot\(\):'
    ]
    
    main_success = True
    for pattern in main_patterns:
        if re.search(pattern, budgetbot_content):
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            main_success = False
    
    if main_success:
        print("   ✅ SUCCESS: BudgetBot main implementation complete")
    else:
        print("   ❌ FAILURE: BudgetBot main implementation incomplete")
        return False
    
    return True

def test_main_runner_integration():
    """Test that main runners include BudgetBot."""
    print("\n🧪 Testing Main Runner Integration")
    print("=" * 50)
    
    # Test main.py
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        main_patterns = [
            r'elif bot_name == "budgetbot":',
            r'from budgetbot import budgetbot',
            r'budgetbot\.run_bot\(\)',
            r'Available bots: themethodbot, budgetbot'
        ]
        
        main_integration_success = True
        for pattern in main_patterns:
            if re.search(pattern, main_content):
                print(f"   ✅ Found in main.py: {pattern}")
            else:
                print(f"   ❌ Missing in main.py: {pattern}")
                main_integration_success = False
        
    except FileNotFoundError:
        print("   ❌ FAILURE: main.py not found")
        return False
    
    # Test run_all_bots.py
    try:
        with open('run_all_bots.py', 'r', encoding='utf-8') as f:
            run_all_content = f.read()
        
        run_all_patterns = [
            r'def run_budgetbot\(\):',
            r'from budgetbot import budgetbot',
            r'budgetbot_process = multiprocessing\.Process',
            r'budgetbot_process\.start\(\)'
        ]
        
        for pattern in run_all_patterns:
            if re.search(pattern, run_all_content):
                print(f"   ✅ Found in run_all_bots.py: {pattern}")
            else:
                print(f"   ❌ Missing in run_all_bots.py: {pattern}")
                main_integration_success = False
        
    except FileNotFoundError:
        print("   ❌ FAILURE: run_all_bots.py not found")
        return False
    
    if main_integration_success:
        print("   ✅ SUCCESS: Main runner integration complete")
    else:
        print("   ❌ FAILURE: Main runner integration incomplete")
        return False
    
    return True

def test_environment_configuration():
    """Test environment configuration for BudgetBot."""
    print("\n🧪 Testing Environment Configuration")
    print("=" * 50)
    
    try:
        with open('.env.example', 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        env_patterns = [
            r'BUDGET_DISCORD_BOT_TOKEN=',
            r'BUDGET_DISCORD_GUILD_ID='
        ]
        
        env_success = True
        for pattern in env_patterns:
            if re.search(pattern, env_content):
                print(f"   ✅ Found in .env.example: {pattern}")
            else:
                print(f"   ❌ Missing in .env.example: {pattern}")
                env_success = False
        
        if env_success:
            print("   ✅ SUCCESS: Environment configuration present")
        else:
            print("   ❌ FAILURE: Environment configuration incomplete")
            return False
        
    except FileNotFoundError:
        print("   ❌ FAILURE: .env.example not found")
        return False
    
    return True

def main():
    """Run all BudgetBot functionality tests."""
    print("🔧 BUDGETBOT FUNCTIONALITY RESTORATION VERIFICATION")
    print("=" * 70)
    print("Testing BudgetBot restoration and integration with latest improvements:")
    print("• Simplified order summary embed format")
    print("• Current tracking system with persistent embeds")
    print("• Automatic delivered order cleanup")
    print("• Budget-specific branding and configurations")
    print()
    
    # Run all tests
    tests = [
        test_budgetbot_structure,
        test_budgetbot_imports,
        test_budgetbot_configuration,
        test_budgetbot_embed_templates,
        test_budgetbot_main_implementation,
        test_main_runner_integration,
        test_environment_configuration
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    # Summary
    print("\n📊 VERIFICATION RESULTS")
    print("=" * 50)
    
    test_names = [
        "Directory Structure",
        "Module Imports",
        "Configuration",
        "Embed Templates",
        "Main Implementation",
        "Runner Integration",
        "Environment Config"
    ]
    
    for i, (test_name, result) in enumerate(zip(test_names, results)):
        status = "PASSED" if result else "FAILED"
        emoji = "✅" if result else "❌"
        print(f"{emoji} {test_name}: {status}")
    
    if all(results):
        print("\n🎉 BUDGETBOT SUCCESSFULLY RESTORED AND INTEGRATED!")
        print("\n🚀 Expected Functionality:")
        print("   • BudgetBot runs alongside themethodbot")
        print("   • Uses simplified order summary embeds (Location, Restaurant, Items, Subtotal)")
        print("   • Integrates with persistent embed system")
        print("   • Supports automatic delivered order cleanup")
        print("   • Has budget-specific branding (orange colors, budget messaging)")
        print("   • Includes all essential commands: /check, /latestsummary, /track, /open, /close")
        print("   • Uses same pricing model as updated themethodbot")
        print("   • Maintains distinct budget-focused identity")
        print("\n📋 Next Steps:")
        print("   1. Configure environment variables (BUDGET_DISCORD_BOT_TOKEN, etc.)")
        print("   2. Test BudgetBot in Discord server")
        print("   3. Verify command functionality and embed display")
        print("   4. Test order tracking and persistent embeds")
        return True
    else:
        print("\n❌ SOME BUDGETBOT TESTS FAILED - PLEASE REVIEW")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

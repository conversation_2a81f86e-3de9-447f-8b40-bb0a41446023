import asyncio
import discord
from discord.ext import commands
from discord import app_commands
import requests
import json
import re
import os
from dotenv import load_dotenv
import logging
from common.extract_label import extract_label_data
import time
import uuid
import sys
from logging.handlers import RotatingFileHandler
from datetime import datetime
from discord.ui import View, Button
import aiohttp
import traceback
import urllib.parse
import base64
import ssl
from collections import defaultdict
from bs4 import BeautifulSoup
from typing import Dict, Any, Optional, List
import random
import os
from common.check_group_order import (
    make_api_request,
    check_group_order,
    process_group_order
)

# Load environment variables
load_dotenv()

if not os.path.exists('logs'):
    os.makedirs('logs')

# Configure logging
log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')

# Console handler only
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(log_formatter)

# Get the root logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)  # Set to INFO for important logs only

# Remove any existing handlers
for handler in logger.handlers[:]:
    logger.removeHandler(handler)

# Add only console handler
logger.addHandler(console_handler)

TOKEN_2 = os.getenv("TOKEN_2")
UBER_COOKIE = os.getenv("UBER_COOKIE")
PROMO_25_COOKIE = os.getenv('25_PROMO')

RANDOM_NAMES = [
    "Howard", "Kongpaochicken", "IwannaEat_", "Glitchyz", "Isaiah",
    "Hammah Khan", "Mr. Method", "The method", "Parnil", "PNN21"
]

DEFAULT_HEADERS = {
    "accept": "*/*",
    "accept-language": "en-US,en;q=0.9",
    "content-type": "application/json",
    "cookie": PROMO_25_COOKIE if PROMO_25_COOKIE else "",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "x-csrf-token": "x",
    "origin": "https://www.ubereats.com",
    "referer": "https://www.ubereats.com/",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Add this list at the top of the file with other imports
COMPLETION_GIFS = [
    "https://c.tenor.com/De8gDcguE70AAAAd/tenor.gif",
    "https://c.tenor.com/sjwmn6VDG3MAAAAd/tenor.gif",
    "https://c.tenor.com/zDChDUnpKpAAAAAd/tenor.gif",
    "https://c.tenor.com/i1stwMn0fowAAAAd/tenor.gif",
    "https://c.tenor.com/rQny9TWXi0IAAAAd/tenor.gif"
]

async def extract_group_link(message):
    """Extract Uber Eats group order link from message content or embeds."""
    pattern = r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)"

    # Check message content
    match = re.search(pattern, message.content)
    if match:
        return match.group(0)

    # Check embeds
    for embed in message.embeds:
        # Check embed description
        if embed.description:
            match = re.search(pattern, embed.description)
            if match:
                return match.group(0)

        # Check embed title
        if embed.title:
            match = re.search(pattern, embed.title)
            if match:
                return match.group(0)

        # Check embed fields
        for field in embed.fields:
            match = re.search(pattern, field.value)
            if match:
                return match.group(0)

    return None

# Status history management functions for persistent embeds
def add_status_to_history(status_history: List[Dict], status: str, timestamp: Optional[int] = None) -> List[Dict]:
    """Add a new status update to the history with Discord timestamp formatting."""
    if timestamp is None:
        timestamp = int(time.time())

    # Create status entry
    status_entry = {
        'status': status,
        'timestamp': timestamp,
        'discord_timestamp': f"<t:{timestamp}:t>"  # Discord time format
    }

    # Add to history if it's not a duplicate
    if not status_history or status_history[-1]['status'] != status:
        status_history.append(status_entry)

    return status_history

def format_status_history(status_history: List[Dict]) -> str:
    """Format the status history for display in embed description."""
    if not status_history:
        return "No status updates yet."

    formatted_lines = []
    for entry in status_history:
        formatted_lines.append(f"{entry['discord_timestamp']} {entry['status']}")

    return "\n".join(formatted_lines)

def get_status_display_text(order_status: str) -> str:
    """Convert order status codes to user-friendly display text."""
    status_mapping = {
        "EnrouteToRestaurant": "Driver is heading to the store",
        "AtRestaurant": "Driver has arrived at the store",
        "EnrouteToDropoff": "Driver is on the way to you",
        "EnrouteToEater": "Driver is on the way to you",
        "TimeToMeetCourier": "The driver is about to drop off your order!",
        "ArrivedAtDropoff": "The driver is about to drop off your order!",
        "DELIVERED": "Order delivered!",
        "CANCELED": "Order canceled"
    }

    return status_mapping.get(order_status, f"Status: {order_status}")

class OrderStatusButtons(discord.ui.View):
    """View class for order status tracking with Status History button."""

    def __init__(self, order_id: str, active_tracking_dict: Dict = None):
        super().__init__(timeout=None)
        self.order_id = order_id
        self.active_tracking_dict = active_tracking_dict or {}

    @discord.ui.button(label="📋 Status History", style=discord.ButtonStyle.secondary)
    async def status_history_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Show the complete status history for this order."""
        try:
            # Get tracking data for this order
            tracking_data = self.active_tracking_dict.get(self.order_id, {})
            status_history = tracking_data.get('status_history', [])

            if not status_history:
                await interaction.response.send_message(
                    "📋 No status history available for this order yet.",
                    ephemeral=True
                )
                return

            # Create embed with full status history
            embed = discord.Embed(
                title="📋 Order Status History",
                description=f"**Order ID:** `{self.order_id}`",
                color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
            )

            # Format status history
            history_text = format_status_history(status_history)
            embed.add_field(
                name="🕒 Timeline",
                value=history_text,
                inline=False
            )

            # Add order link if available
            order_link = tracking_data.get('order_link')
            if order_link:
                embed.add_field(
                    name="🔗 Order Link",
                    value=f"[View on Uber Eats]({order_link})",
                    inline=False
                )

            embed.set_footer(text="The Method | Order Tracking")
            embed.timestamp = discord.utils.utcnow()

            await interaction.response.send_message(embed=embed, ephemeral=True)

        except Exception as e:
            logging.error(f"Error in status history button: {str(e)}")
            await interaction.response.send_message(
                "❌ An error occurred while retrieving status history.",
                ephemeral=True
            )

def create_locked_order_embed():
    """Create embed for locked/canceled orders."""
    return discord.Embed(
        title="🔒 Group Order is Locked/Canceled",
        description=(
            "This group order appears to be locked or canceled.\n\n"
            "1. Go back to your Uber Eats Cart\n"
            "2. Press Unlock Group Order\n"
            "3. Resend Cart"
        ),
        color=discord.Color.red()
    )

def create_non_promo_embed():
    """Create embed for stores not in promo."""
    embed = discord.Embed(
        title="⚠️ Store Not in Promo",
        description="Follow these steps to find eligible stores:",
        color=discord.Color.yellow()
    )

    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1349998616110043237/image.png?ex=67d86f6f&is=67d71def&hm=43a18ef135008ca5afa451b11e762db1f2653cb163051d25e7194a16e87305a7&=&format=webp&quality=lossless")

    embed.add_field(
        name="Steps to Find Eligible Stores",
        value=(
            "**PLEASE DO NOT USE DISCORD BROWSER**\n"
            "1. **Open this link in your Chrome/Safari Browser:**\n"
            "[Eligible Restaurants](https://tinyurl.com/TheMethodUE)\n"
            "\n"
            "2. **Enter Your Address**\n"
            "The link will take you to Uber Eats. Log in and Input your address and proceed.\n"
            "\n"
            "3. **Open the Link in your Chrome/Safari Browser again**\n"
            "[Eligible Restaurants](https://tinyurl.com/TheMethodUE)\n"
            "\n"
            "4. **Done!**\n"
            "You'll now see all eligible restaurants near you."
        ),
        inline=False
    )
    return embed

def process_cart_items(cart_items_data):
    """Process cart items and calculate totals."""
    cart_items = []
    total_before_discount = 0

    for item in cart_items_data:
        base_price = float(item.get('price', 0)) / 100  # Convert cents to dollars
        quantity = int(item.get('quantity', 1))
        title = item.get('title', 'Unknown Item')

        # Process customizations
        addon_price = 0
        addon_names = []
        for group_key, customization_list in item.get('customizations', {}).items():
            for customization in customization_list:
                price = float(customization.get('price', 0)) / 100  # Convert cents to dollars
                if price > 0:
                    addon_price += price
                    addon_names.append(customization.get('title', ''))

        total_item_price = base_price + addon_price
        total_with_quantity = total_item_price * quantity

        item_text = title
        if addon_names:
            paid_addons = [name for name in addon_names if name]
            if paid_addons:
                item_text += f" ({', '.join(paid_addons)})"

        cart_items.append(f"╰・x{quantity} {item_text} (${total_item_price:.2f})")
        total_before_discount += total_with_quantity

    return cart_items, round(total_before_discount, 2)

def calculate_fees(fees_data: dict, subtotal: float, is_cad: bool = False) -> dict:
    """Calculate all fee-related values."""
    # Always use the provided subtotal parameter
    actual_subtotal = subtotal

    # Set fixed discount amount based on region
    fixed_discount = 26.00 if is_cad else 20.00  # CAD gets $26 discount (30% higher than USD $20)

    # Calculate discounted subtotal and savings
    discounted_subtotal = round(max(0, actual_subtotal - fixed_discount), 2)  # Ensure non-negative
    savings = round(actual_subtotal - discounted_subtotal, 2)

    # Calculate overflow fee
    threshold = 30 if is_cad else 25
    overflow_fee = round(max(0, actual_subtotal - threshold), 2)

    # Extract fees from fees_data
    service_fee = float(fees_data.get('service_fee', 0))
    delivery_fee = float(fees_data.get('delivery_fee', 0))
    ca_driver_benefit = float(fees_data.get('ca_driver_benefit', 0))
    taxes = float(fees_data.get('taxes', 0))
    uber_one_discount = float(fees_data.get('uber_one_discount', 0))

    # Add method fee component - $7.50 for USD, $9.75 for CAD (30% higher)
    method_fee = 9.75 if is_cad else 7.50

    # Calculate total fees (excluding overflow fee since it's handled separately, but including method fee)
    total_fees = round(service_fee + delivery_fee + ca_driver_benefit + taxes + method_fee, 2)
    final_fees = round(total_fees - uber_one_discount, 2)

    # Calculate final total (now properly including overflow fee)
    final_total = round(discounted_subtotal + overflow_fee + final_fees, 2)

    return {
        'subtotal': actual_subtotal,
        'discounted_subtotal': discounted_subtotal,
        'savings': savings,
        'fixed_discount': fixed_discount,
        'service_fee': service_fee,
        'delivery_fee': delivery_fee,
        'ca_driver_benefit': ca_driver_benefit,
        'taxes': taxes,
        'overflow_fee': overflow_fee,
        'method_fee': method_fee,
        'fixed_fee': method_fee,  # Keep for backward compatibility
        'total_fees': total_fees,
        'uber_one_discount': uber_one_discount,
        'final_fees': final_fees,
        'final_total': final_total,
        'is_cad': is_cad
    }

def create_order_summary_embed(result, cart_items, fee_calculations):
    """Create the main order summary embed."""
    embed = discord.Embed(
        title="Group Order Summary (BETA)",
        color=discord.Color.green()
    )

    # Add group order link if available
    group_link = result.get('group_link', 'Not available')
    embed.description = f"**🏷️ Promo Status:** ✅ Store is in promo!\n**🔗 Group Order Link:** {group_link}\n\n"

    # Location
    location = result.get('location', {})
    embed.add_field(
        name="📍 Location",
        value=f"{location.get('address', 'Unknown Address')}, {location.get('city', 'Unknown')}, {location.get('state', 'XX').upper()}",
        inline=False
    )

    # Rest of the function remains the same...
    # Cart Items
    if cart_items:
        embed.add_field(
            name="🛒 Cart Items",
            value="\n".join(cart_items),
            inline=False
        )

    # Financial Information
    currency = "$"

    # Original and discounted totals
    embed.add_field(
        name="💸 Original Total",
        value=f"{currency}{fee_calculations['subtotal']:.2f}",
        inline=False
    )
    embed.add_field(
        name="💰 Discounted Total",
        value=f"{currency}{fee_calculations['discounted_subtotal']:.2f}",
        inline=True
    )
    embed.add_field(
        name="🤑 You Save",
        value=f"{currency}{fee_calculations['savings']:.2f}",
        inline=True
    )

    # Only show overflow fee as a separate field if it exists
    if fee_calculations['overflow_fee'] > 0:
        embed.add_field(
            name="⚠️ Overflow Fee",
            value=f"{currency}{fee_calculations['overflow_fee']:.2f}",
            inline=False
        )

    # Fees breakdown
    fees_text = (
        f"Service Fee: {currency}{fee_calculations['service_fee']:.2f}\n"
        f"CA Driver Benefit: {currency}{fee_calculations['ca_driver_benefit']:.2f}\n"
        f"Taxes: {currency}{fee_calculations['taxes']:.2f}\n"
        f"Total Fees: {currency}{fee_calculations['total_fees']:.2f}\n"
        f"Uber One Discount: -{currency}{fee_calculations['uber_one_discount']:.2f}\n"
        f"Final Fees: {currency}{fee_calculations['final_fees']:.2f}"
    )

    embed.add_field(
        name="📊 Fees (NOTE: BOGO's/FREE ITEM PROMOS INCREASE TAX)",
        value=fees_text,
        inline=False
    )

    # Final total
    final_total = fee_calculations['discounted_subtotal'] + fee_calculations['overflow_fee'] + fee_calculations['final_fees']
    embed.add_field(
        name="🧾 Final Total (tip not included)",
        value=f"{currency}{final_total:.2f}",
        inline=False
    )

    embed.set_footer(text="Note: Final price may vary slightly. Please wait for a chef to confirm.")

    return embed

def check_order_limits(subtotal, is_cad):
    """Check if order meets minimum/maximum requirements."""
    currency = "CA$" if is_cad else "$"
    min_order = 30.0 if is_cad else 25.0
    max_order = 35.0 if is_cad else 30.0

    if subtotal < min_order:
        return discord.Embed(
            title="⚠️ Minimum Order Requirement",
            description=f"Your subtotal must be at least {currency}{min_order:.2f} before fees and taxes.\nPlease add more items to your cart.",
            color=discord.Color.yellow()
        )
    elif subtotal > max_order:
        return discord.Embed(
            title="⚠️ Maximum Order Limit Exceeded",
            description=f"Your subtotal should be {currency}{max_order:.2f} or less before fees and taxes.\nPlease remove some items from your cart.",
            color=discord.Color.yellow()
        )
    return None

async def check_store_promo(store_url, cookie_value=None):
    """Check if store is in promo."""
    try:
        # Parse the URL to separate base URL and parameters
        parsed_url = urllib.parse.urlparse(store_url)
        base_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
        existing_params = dict(urllib.parse.parse_qs(parsed_url.query))

        # Ensure diningMode parameter is set
        params = {
            "diningMode": existing_params.get('diningMode', ['DELIVERY'])[0]
        }

        # Add other existing parameters
        for key, value in existing_params.items():
            if key != 'diningMode':
                params[key] = value[0]

        # Get cookie value
        cookie = cookie_value or os.getenv('25_PROMO')
        if not cookie:
            logging.error("No cookie value provided and no 25_PROMO environment variable found")
            return False

        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
            "Cookie": f"25_PROMO={cookie}"
        }

        logging.info(f"Checking promo status for URL: {base_url}")

        # Use a timeout to prevent hanging
        timeout = aiohttp.ClientTimeout(total=15)  # 15 second timeout

        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.get(base_url, params=params, headers=headers) as response:
                    if response.status != 200:
                        logging.error(f"Error response from store URL: {response.status}")
                        return False

                    content = await response.text()
                    content_length = len(content)
                    logging.info(f"Received content length: {content_length} bytes")

                    if content_length < 100:  # Too short to be a valid response
                        logging.error(f"Response content too short: {content}")
                        return False

                    cleaned_content = content.replace('\\u0022', '"')

                    # Check for both promo UUIDs
                    promo_uuid_1 = "2c1e668b-8d83-42a4-99db-985c72fa74de"  # US promo
                    promo_uuid_2 = "807f13d5-0e00-4df2-9894-6b23e73e9fa6"  # Canada promo

                    # Additional UUIDs to check
                    additional_uuids = [
                        "ae2e9f79-6189-4f46-bf20-5b41debb570a",  # Old US promo UUID
                        "449baf92-dda2-44d1-9ad6-24033c26f516",
                        "c8e35ed9-3e53-4c12-a63a-7eb5a683a64f",
                        "4a6e5b6a-6784-4d83-8b90-9e9e4b3b7119",
                        "1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d"
                    ]

                    # Check for primary UUIDs in different formats
                    is_promo_1 = f'"uuid":"{promo_uuid_1}"' in cleaned_content or promo_uuid_1 in cleaned_content
                    is_promo_2 = f'"uuid":"{promo_uuid_2}"' in cleaned_content or promo_uuid_2 in cleaned_content

                    # Check for additional UUIDs
                    additional_matches = []
                    for i, uuid in enumerate(additional_uuids):
                        is_match = f'"uuid":"{uuid}"' in cleaned_content
                        additional_matches.append(is_match)
                        logging.info(f"Additional UUID {i+1} check: {uuid} - Found: {is_match}")

                    # Also check for any UUID pattern
                    uuid_pattern = r'"uuid":"([a-f0-9-]+)"'
                    import re
                    found_uuids = re.findall(uuid_pattern, cleaned_content)
                    logging.info(f"All UUIDs found in response: {found_uuids}")

                    # Debug logging
                    logging.info(f"Response status: {response.status}")
                    logging.info(f"Promo UUID 1 check: {promo_uuid_1} - Found: {is_promo_1}")
                    logging.info(f"Promo UUID 2 check: {promo_uuid_2} - Found: {is_promo_2}")

                    # Save a sample of the content for debugging
                    with open('promo_response_sample.txt', 'w') as f:
                        f.write(cleaned_content[:10000])  # Save first 10K characters
                    logging.info("Saved response sample to promo_response_sample.txt")

                    # Store is in promo if any UUID is found
                    is_promo = is_promo_1 or is_promo_2 or any(additional_matches)

                    if is_promo:
                        logging.info("✅ Store is in promo!")
                    else:
                        logging.info("❌ Store is not in promo")

                    return is_promo
            except asyncio.TimeoutError:
                logging.error("Timeout while checking store promo")
                return False
            except Exception as e:
                logging.error(f"Error during store promo request: {str(e)}")
                return False

    except Exception as e:
        logging.error(f"Error checking store promo: {str(e)}")
        return False

def extract_uuid_from_link(link: str) -> str:
    """Extract UUID from Uber Eats group order link."""
    pattern = r"(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)([a-zA-Z0-9-]+)(?:/join)?"
    match = re.search(pattern, link)
    if not match:
        raise ValueError("Invalid Uber Eats group order link format")
    return match.group(1)

async def make_api_request(session: aiohttp.ClientSession, endpoint: str, payload: dict) -> dict:
    try:
        url = f"https://www.ubereats.com/_p/api/{endpoint}"
        timeout = aiohttp.ClientTimeout(total=10)

        logger.debug(f"Making API request to: {url}")
        logger.debug(f"Payload: {payload}")

        async with session.post(
            url,
            json=payload,
            headers=DEFAULT_HEADERS,
            timeout=timeout
        ) as response:
            response_data = await response.json()
            logger.debug(f"API Response: {json.dumps(response_data, indent=2)}")
            return {
                "status": response.status,
                "data": response_data
            }
    except Exception as e:
        logger.error(f"API request failed: {str(e)}")
        logger.exception("Full exception:")
        return {
            "status": 500,
            "error": str(e)
        }

async def get_checkout_presentation(session: aiohttp.ClientSession, store_uuid: str, cart_uuid: str, group_uuid: str) -> dict:
    """Get checkout presentation data from the API."""
    try:
        # First set user consent
        consent_success = await set_user_consent(session)
        if not consent_success:
            logging.error("Failed to set user consent")
            return None

        checkout_payload = {
            "payloadTypes": [
                "canonicalProductStorePickerPayload",
                "cartItems",
                "subtotal",
                "basketSize",
                "promotion",
                "restrictedItems",
                "venueSectionPicker",
                "locationInfo",
                "upsellCatalogSections",
                "subTotalFareBreakdown",
                "storeSwitcherActionableBannerPayload",
                "fareBreakdown",
                "promoAndMembershipSavingBannerPayload",
                "passBanner",
                "passBannerOnCartPayload"
            ],
            "draftOrderUUID": group_uuid,
            "isGroupOrder": True,
            "cartUUID": cart_uuid,
            "storeUUID": store_uuid
        }

        result = await make_api_request(session, "getCheckoutPresentationV1", checkout_payload)

        if result["status"] != 200:
            logging.error(f"Failed to get checkout presentation: {result.get('data', {}).get('message')}")
            return None

        # Navigate through the response structure
        response_data = result.get('data', {})
        data = response_data.get('data', {})
        checkout_payloads = data.get('checkoutPayloads', {})

        # Extract both fareBreakdown and passBanner
        fare_breakdown = checkout_payloads.get('fareBreakdown', {})
        pass_banner = checkout_payloads.get('passBanner', {})

        if not fare_breakdown:
            logging.error("No fareBreakdown in checkoutPayloads")
            return None

        return {
            'fareBreakdown': fare_breakdown,
            'passBanner': pass_banner
        }

    except Exception as e:
        logging.error(f"Error getting checkout presentation: {str(e)}")
        return None

async def get_checkout_flow(session: aiohttp.ClientSession, group_order_uuid: str) -> dict:
    try:
        order_details = await get_order_details_from_data(session, group_order_uuid)
        if not order_details or not order_details.get('cart_items'):
            logging.error("Failed to get order details")
            return None

        max_retries = 3
        for attempt in range(max_retries):
            draft_order_payload = {
                "isMulticart": True,
                "currencyCode": "USD",
                "deliveryType": "ASAP",
                "deliveryTime": {
                    "asap": True
                },
                "interactionType": "door_to_door",
                "useCredits": True,
                "checkMultipleDraftOrdersCap": True,
                "actionMeta": {
                    "isQuickAdd": False,
                    "numClicks": 0
                },
                "businessDetails": {},
                "extraPaymentProfiles": [],
                "promotionOptions": {
                    "autoApplyPromotionUUIDs": [],
                    "selectedPromotionInstanceUUIDs": [],
                    "skipApplyingPromotion": False
                },
                "shoppingCartItems": [{
                    "uuid": "0ba128da-ec26-5dab-bdbf-043f1112410f",
                    "shoppingCartItemUuid": "13080455-0bea-47e0-85ff-c591c9d793a0",
                    "storeUuid": "8f01b38a-d588-4511-bcfa-a4a8daa90305",
                    "sectionUuid": "833e29e1-dba0-5a06-8f85-a95fe25011ed",
                    "subsectionUuid": "16c1e0cc-d2f1-5b61-ad7e-0a750d39f170",
                    "price": 649,
                    "title": "Jumbo Jack®",
                    "quantity": 2,
                    "customizations": {},
                    "imageURL": "https://tb-static.uber.com/prod/image-proc/processed_images/2f8cbb3ac18dc27293a95f4a92ff09a4/a19bb09692310dfd41e49a96c424b3a6.jpeg",
                    "specialInstructions": "",
                    "itemId": None
                }],
                "deliveryLocation": {
                    "address": {
                        "address1": order_details['address'],
                        "city": order_details['city'],
                        "state": order_details['state'],
                        "zipcode": order_details['zipcode'],
                        "country": "US",
                        "reference": order_details['reference'],
                        "referenceType": order_details['referenceType']
                    },
                    "latitude": order_details['latitude'],
                    "longitude": order_details['longitude'],
                    "type": "delivery"
                }
            }

            draft_result = await make_api_request(session, "createDraftOrderV2", draft_order_payload)

            if draft_result.get('status') == 200 and draft_result.get('data', {}).get('status') != 'failure':
                logging.info("Draft order created successfully")
                # Extract location from draft order response
                draft_data = draft_result.get('data', {}).get('data', {})
                delivery_location = draft_data.get('deliveryLocation', {})
                address = delivery_location.get('address', {})

                # Update order_details with actual location data
                order_details.update({
                    'city': address.get('city'),
                    'state': address.get('state'),
                    'zipcode': address.get('zipcode'),
                    'address': address.get('address1')
                })

                return draft_result

            error_code = draft_result.get('data', {}).get('data', {}).get('code')
            error_message = draft_result.get('data', {}).get('data', {}).get('message')

            if error_code == '500':
                if attempt < max_retries - 1:
                    logging.warning(f"Attempt {attempt + 1} failed with 500 error, retrying...")
                    await asyncio.sleep(1)
                    continue
                else:
                    logging.error("All retry attempts failed, falling back to checkout presentation")
            elif error_code == '401' and error_message == 'NO_DELIVERY_LOCATION':
                logging.error("Missing or invalid delivery location")
                return None
            else:
                logging.error(f"Failed to create draft order - Business error: {draft_result}")
                return None

        return None

    except Exception as e:
        logging.error(f"Error in checkout flow: {str(e)}")
        return None

def extract_pass_banner_info(checkout_data: dict) -> dict:
    """Extract Uber One discount information from pass banner."""
    try:
        pass_banner = checkout_data.get('passBanner', {})
        if not pass_banner or not pass_banner.get('banners'):
            return {'uber_one_discount': 0.0}

        savings = pass_banner['banners'][0].get('savingsAmountWithCurrencySymbol', '$0.00')
        uber_one_discount = float(savings.replace('$', ''))

        return {
            'uber_one_discount': uber_one_discount,
            'savings_text': savings
        }

    except Exception as e:
        logging.error(f"Error extracting pass banner info: {str(e)}")
        return {'uber_one_discount': 0.0}

def extract_fees_from_charges(charges: list) -> dict:
    """Extract fees from charges list."""
    fees = {
        "subtotal": "0.00",
        "delivery_fee": "0.00",
        "service_fee": "0.00",
        "ca_driver_benefit": "0.00",
        "taxes": "0.00"
    }

    for charge in charges:
        title = charge.get('title', {}).get('text', '').lower()
        value_text = charge.get('value', {}).get('text', '0.00')

        if '\u00a0' in value_text:  # Split on non-breaking space
            value_text = value_text.split('\u00a0')[-1]  # Take the last value (current price)

        # Remove currency symbol and clean up
        value = value_text.replace('$', '').replace('CA', '').strip()

        logging.debug(f"Extracted fee - Title: {title}, Value: {value}")

        if "subtotal" in title:
            fees["subtotal"] = value
        elif "delivery fee" in title:
            fees["delivery_fee"] = value
        elif "taxes & other fees" in title:
            # Get the info bottom sheet paragraphs
            paragraphs = charge.get('action', {}).get('infoBottomSheet', {}).get('paragraphs', [])

            for paragraph in paragraphs:
                p_title = paragraph.get('title', '').lower()
                end_title = paragraph.get('endTitle', '$0.00').replace('$', '').strip()

                if 'service fee' in p_title:
                    fees["service_fee"] = end_title
                elif 'ca driver benefit' in p_title:
                    fees["ca_driver_benefit"] = end_title
                elif 'taxes' == p_title:
                    fees["taxes"] = end_title

    return fees

async def set_user_consent(session: aiohttp.ClientSession) -> bool:
    """Set user consent before accessing checkout."""
    try:
        payload = {
            "name": "gdpr",
            "value": True
        }

        result = await make_api_request(session, "setUserConsentV1", payload)
        return result["status"] == 200
    except Exception as e:
        logging.error(f"Error setting user consent: {str(e)}")
        return False

async def get_draft_order(session: aiohttp.ClientSession, group_uuid: str) -> dict:
    """Get draft order details."""
    try:
        payload = {
            "draftOrderUUID": group_uuid
        }

        result = await make_api_request(session, "getDraftOrderByUuidV2", payload)
        if result["status"] == 200:
            return result.get("data", {})
        return None
    except Exception as e:
        logging.error(f"Error getting draft order: {str(e)}")
        return None

def extract_fees_from_paragraphs(paragraphs: list) -> dict:
    """Extract all fees from the paragraphs list."""
    fees = {
        "service_fee": 0.0,
        "ca_driver_benefit": 0.0,
        "uber_one_discount": 0.0,
        "taxes": 0.0
    }

    for paragraph in paragraphs:
        title = paragraph.get('title', '').lower()
        end_title = paragraph.get('endTitle', '0.00')

        # Clean up the value (remove currency symbol and convert to float)
        value = float(end_title.replace('$', '').replace('CA', '').strip())

        # Extract specific fees
        if 'service fee' in title:
            fees["service_fee"] = value
        elif 'ca driver benefit' in title:
            fees["ca_driver_benefit"] = value
        elif 'uber one' in title or 'off with uber' in title:
            fees["uber_one_discount"] = abs(value)  # Make positive
        elif 'taxes' in title:
            fees["taxes"] = value

    # Calculate total fees
    total_fees = (fees["service_fee"] + fees["ca_driver_benefit"] -
                 fees["uber_one_discount"] + fees["taxes"])

    fees["total_fees"] = total_fees

    return fees

async def calculate_final_fees(session, store_id, cart_uuid, group_uuid):
    try:
        # Get checkout presentation
        checkout_data = await get_checkout_presentation(session, store_id, cart_uuid, group_uuid)
        if not checkout_data:
            return None

        # Get fare breakdown
        fare_breakdown = checkout_data.get('fareBreakdown', {})
        charges = fare_breakdown.get('charges', [])

        # Get basic fees
        fees = extract_fees_from_charges(charges)

        # Convert basic fees to float
        fee_values = {}
        for key, value in fees.items():
            try:
                cleaned_value = value.replace('CA', '').replace('$', '').strip()
                fee_values[key] = float(cleaned_value)
            except (ValueError, TypeError) as e:
                logging.error(f"Error converting fee {key} ({value}): {str(e)}")
                fee_values[key] = 0.0

        # Calculate Uber One discount (11% of subtotal)
        subtotal = fee_values.get('subtotal', 0.0)
        uber_one_discount = round(subtotal * 0.11, 2)

        # Add method fee component - $7.50 for USD, $9.75 for CAD (30% higher)
        # Determine if this is a Canadian order (this function doesn't have is_cad parameter,
        # so we'll default to USD for now - this may need to be passed in later)
        method_fee = 7.50  # Default to USD, may need to be updated to detect CAD orders

        # Calculate total fees before Uber One discount (including method fee)
        total_fees_before_discount = (fee_values.get('service_fee', 0.0) +
                                    fee_values.get('ca_driver_benefit', 0.0) +
                                    fee_values.get('taxes', 0.0) +
                                    method_fee)

        # Calculate final total fees after Uber One discount
        final_total_fees = total_fees_before_discount - uber_one_discount

        result = {
            'subtotal': subtotal,
            'delivery_fee': fee_values.get('delivery_fee', 0.0),
            'service_fee': fee_values.get('service_fee', 0.0),
            'ca_driver_benefit': fee_values.get('ca_driver_benefit', 0.0),
            'uber_one_discount': uber_one_discount,
            'taxes': fee_values.get('taxes', 0.0),
            'method_fee': method_fee,
            'fixed_fee': method_fee,  # Keep for backward compatibility
            'total_fees': final_total_fees,
            'total_fees_before_discount': total_fees_before_discount,
            'has_free_delivery': fee_values.get('delivery_fee', 0.0) < 0.01
        }

        logging.info(f"Calculated fees: {json.dumps(result, indent=2)}")
        return result

    except Exception as e:
        logging.error(f"Error calculating final fees: {str(e)}")
        logging.debug("Full error:", exc_info=True)
        return {
            'subtotal': 0.0,
            'delivery_fee': 0.0,
            'service_fee': 0.0,
            'ca_driver_benefit': 0.0,
            'uber_one_discount': 0.0,
            'taxes': 0.0,
            'total_fees': 0.0,
            'total_fees_before_discount': 0.0,
            'has_free_delivery': False
        }

async def validate_session(session):
    test_result = await make_api_request(session, "getActiveOrdersV1", {})
    return test_result['status'] == 200

def extract_delivery_details_from_url(url: str) -> dict:
    """Extract delivery details from the pl parameter in URL."""
    try:
        # Extract and decode the pl parameter
        pl_match = re.search(r'pl=([^&]+)', url)
        if pl_match:
            pl_data = base64.b64decode(pl_match.group(1)).decode('utf-8')
            delivery_json = json.loads(pl_data)

            return {
                "location": {
                    "address": {
                        "address1": delivery_json.get("address", ""),
                        "reference": delivery_json.get("reference", ""),
                        "referenceType": delivery_json.get("referenceType", "uber_places"),
                        "country": "US"
                    },
                    "latitude": delivery_json.get("latitude"),
                    "longitude": delivery_json.get("longitude")
                }
            }
    except Exception as e:
        logging.error(f"Error extracting delivery details: {str(e)}")
        return {}

async def get_order_details_from_data(data: dict) -> dict:
    """Extract order details from API response data."""
    try:
        if not isinstance(data, dict):
            return None

        # Get store UUID
        store_uuid = data.get('storeUuid')
        if not store_uuid:
            logging.error("No store UUID found in response")
            return None

        delivery_address = data.get('deliveryAddress', {})
        if not delivery_address:
            logging.error("No delivery address found in response")
            return None

        address_details = delivery_address.get('address', {})

        # Extract city and state from subtitle
        subtitle = address_details.get('subtitle', '')
        city = state = ""
        if subtitle:
            parts = subtitle.split(', ')
            if len(parts) >= 2:
                city = parts[-2]
                state = parts[-1]

        return {
            'store_id': store_uuid,
            'cart_items': data.get('shoppingCart', {}).get('items', []),
            'address': address_details.get('address1', ''),
            'city': city,
            'state': state,
            'zipcode': address_details.get('zipcode'),
            'latitude': delivery_address.get('latitude'),
            'longitude': delivery_address.get('longitude'),
            'reference': delivery_address.get('reference', ''),
            'referenceType': delivery_address.get('referenceType', 'uber_places')
        }

    except Exception as e:
        logging.error(f"Error getting order details: {str(e)}")
        return None

async def extract_store_id(url: str) -> tuple[str, str]:
    """Extract store ID from UberEats URL."""
    try:
        # Match store ID pattern from URL
        # Example: /store/mcdonalds-junipero-serra/2ty6Z2bzTm-qM_B7PL8sKg
        store_id_match = re.search(r'/store/[^/]+/([^?]+)', url)
        if store_id_match:
            return store_id_match.group(1), "store"

        # If no match found
        return None, None

    except Exception as e:
        logging.error(f"Error extracting store ID: {str(e)}")
        return None, None

def parse_store_url(url: str) -> dict:
    """Parse and log all components of a store URL."""
    try:
        parsed = urllib.parse.urlparse(url)
        query_params = urllib.parse.parse_qs(parsed.query)

        # Extract store ID using regex
        store_id_match = re.search(r"/store/[^/]+/([a-zA-Z0-9-_]+)", parsed.path)
        if not store_id_match:
            logging.error("Could not extract store ID from URL")
            return None

        store_id = store_id_match.group(1)

        # Decode the location payload
        encoded_pl = query_params.get('pl', [None])[0]
        location_data = None
        if encoded_pl:
            try:
                decoded_pl = base64.b64decode(encoded_pl).decode('utf-8')
                location_data = json.loads(decoded_pl)
            except Exception as e:
                logging.error(f"Error decoding location payload: {str(e)}")

        # Create return dictionary with safe defaults
        return_data = {
            'store_id': store_id,
            'location_data': location_data or {},
            'query_params': dict(query_params)
        }

        # Log parsed data
        logging.info("=== Store URL Analysis ===")
        logging.info(f"Store ID: {store_id}")
        logging.info(f"Query Parameters: {json.dumps(dict(query_params), indent=2)}")
        logging.info(f"Location Data: {json.dumps(location_data, indent=2)}")

        return return_data

    except Exception as e:
        logging.error(f"Error parsing store URL: {str(e)}")
        return None

async def check_group_order(url: str) -> bool:
    """Check if the group order is valid and accessible."""
    try:
        # Extract UUID from the URL
        uuid_match = re.search(r'group-orders/([a-zA-Z0-9-]+)', url)
        if not uuid_match:
            logging.error("Failed to extract UUID from group order URL")
            return False

        group_uuid = uuid_match.group(1)

        async with aiohttp.ClientSession(cookies={'25_PROMO': os.getenv('25_PROMO')}) as session:
            if not await validate_session(session):
                logging.error("Invalid session")
                return False

            # Get cart details to verify the group order is valid
            order_details = await get_order_details_from_data(session, group_uuid)
            if not order_details:
                logging.error("Failed to get order details")
                return False

            return True

    except Exception as e:
        logging.error(f"Error checking group order: {str(e)}")
        return False

async def get_order_details_from_data(session: aiohttp.ClientSession, data: dict) -> dict:
    """Extract order details from API response data."""
    try:
        if not isinstance(data, dict):
            return None

        # Get store UUID
        store_uuid = data.get('storeUuid')
        if not store_uuid:
            logging.error("No store UUID found in response")
            return None

        delivery_address = data.get('deliveryAddress', {})
        if not delivery_address:
            logging.error("No delivery address found in response")
            return None

        address_details = delivery_address.get('address', {})

        # Extract city and state from subtitle
        subtitle = address_details.get('subtitle', '')
        city = state = ""
        if subtitle:
            parts = subtitle.split(', ')
            if len(parts) >= 2:
                city = parts[-2]
                state = parts[-1]

        return {
            'store_id': store_uuid,
            'cart_items': data.get('shoppingCart', {}).get('items', []),
            'address': address_details.get('address1', ''),
            'city': city,
            'state': state,
            'zipcode': address_details.get('zipcode'),
            'latitude': delivery_address.get('latitude'),
            'longitude': delivery_address.get('longitude'),
            'reference': delivery_address.get('reference', ''),
            'referenceType': delivery_address.get('referenceType', 'uber_places')
        }

    except Exception as e:
        logging.error(f"Error getting order details: {str(e)}")
        return None

async def process_item(item: Dict[str, Any]) -> Dict[str, Any]:
    logging.info(f"Processing item data:\n{json.dumps(item, indent=2)}")

    result = {
        'title': item.get('title', 'Unknown Item'),
        'price': float(item.get('price', 0)),
        'quantity': item.get('quantity', 1),
        'promos': []
    }

    # Check for promoInfo
    if 'promoInfo' in item:
        promo_info = item['promoInfo']
        if isinstance(promo_info, dict) and 'promoInfoLabel' in promo_info:
            label_info = promo_info['promoInfoLabel']

            # First try accessibilityText
            promo_label = label_info.get('accessibilityText')

            # If not found, try to get from richTextElements
            if not promo_label and 'richTextElements' in label_info:
                for element in label_info['richTextElements']:
                    if (element.get('type') == 'text' and
                        'text' in element and
                        isinstance(element['text'], dict) and
                        'text' in element['text'] and
                        isinstance(element['text']['text'], dict) and
                        'text' in element['text']['text']):
                        promo_label = element['text']['text']['text']
                        break

async def get_store_url_from_data(session: aiohttp.ClientSession, data: dict) -> str:
    try:
        # Get store UUID and delivery details
        store_uuid = data.get('storeUuid')
        delivery_address = data.get('deliveryAddress', {})

        if not store_uuid or not delivery_address:
            # Try alternate data structure
            store_uuid = data.get('store', {}).get('storeUuid')
            delivery_address = data.get('address', {})

            if not store_uuid:
                logging.error("Could not find store UUID in data")
                return None

        # Construct location payload
        location_payload = {
            "address": delivery_address.get('address1', ''),
            "reference": delivery_address.get('reference', ''),
            "referenceType": delivery_address.get('referenceType', 'uber_places'),
            "latitude": delivery_address.get('latitude'),
            "longitude": delivery_address.get('longitude')
        }

        # Get store details from API
        store_result = await make_api_request(session, "getStoreV1", {
            "storeUuid": store_uuid,
            "location": location_payload
        })

        if store_result.get('status') != 200:
            logging.error("Failed to get store details")
            return None

        # Extract store data from nested structure
        store_data = store_result.get('data', {}).get('data', {})
        store_slug = store_data.get('slug')

        if not store_slug:
            logging.error(f"Failed to get store slug. Store data: {store_data}")
            return None

        # Properly encode the location payload
        json_str = json.dumps(location_payload)
        base64_encoded = base64.b64encode(json_str.encode()).decode()
        encoded_pl = urllib.parse.quote(base64_encoded)

        # Construct the final URL
        return (
            f"https://www.ubereats.com/store/{store_slug}/{store_uuid}"
            f"?diningMode=DELIVERY&pl={encoded_pl}&ps=1"
        )

    except Exception as e:
        logging.error(f"Error getting store URL: {str(e)}")
        return None

async def process_group_order(group_link: str) -> dict:
    try:
        uuid_match = re.search(r'group-orders/([a-zA-Z0-9-]+)', group_link)
        if not uuid_match:
            return None

        group_uuid = uuid_match.group(1)
        current_cookie = os.getenv('25_PROMO')
        session = None

        try:
            session = aiohttp.ClientSession(cookies={'25_PROMO': current_cookie})
            # Initial join and data fetch with regular cookie
            join_payload = {
                "draftOrderUuid": group_uuid,
                "nickname": random.choice(RANDOM_NAMES)
            }

            join_result = await make_api_request(session, "addMemberToDraftOrderV1", join_payload)

            if (join_result.get('data', {}).get('data', {}).get('message') == 'cart.not_editable.group_order_locked' or
                join_result.get('data', {}).get('message') == 'cart.not_editable.group_order_locked'):
                return {
                    'error': {
                        'type': 'LOCKED_ORDER',
                        'message': 'cart.not_editable.group_order_locked'
                    }
                }

            data = join_result.get('data', {}).get('data', {})
            if not data:
                logging.error("No data received from join request")
                return None

            # Get store URL for reference (promo checking removed)
            store_url = await get_store_url_from_data(session, data)
            # Promo checking removed - always treat stores as having valid promotions
            is_promo = True

            if store_url:
                logging.info(f"[LINK] Generated store URL: {store_url}")
                logging.info("FINAL Store promo status: [ALWAYS IN PROMO] - Promo checking disabled")
            else:
                logging.error("Failed to generate store URL")

            # Only proceed with these checks if we have valid data
            if not await validate_session(session):
                logging.error("Invalid session")
                return None

            order_details = await get_order_details_from_data(session, data)
            if not order_details:
                logging.error("Failed to get order details")
                return None

            logging.info(f"Cart details: {json.dumps(order_details, indent=2)}")

            # Get checkout presentation and calculate fees
            checkout_data = await get_checkout_presentation(
                session,
                order_details['store_id'],
                data.get('cartUuid'),
                group_uuid
            )

            fees_data = None
            if checkout_data:
                fees_data = await calculate_final_fees(
                    session,
                    order_details['store_id'],
                    data.get('cartUuid'),
                    group_uuid
                )

            return {
                'cart_items': order_details.get('cart_items', []),
                'location': {
                    'address': order_details.get('address'),
                    'city': order_details.get('city'),
                    'state': order_details.get('state'),
                    'zipcode': order_details.get('zipcode')
                },
                'is_promo': is_promo,
                'store_url': store_url,
                'fees': fees_data
            }

        finally:
            if session:
                await session.close()

    except Exception as e:
        logging.error(f"Error processing group order: {str(e)}")
        return None

def extract_order_details(embed_data):
    """Extracts order details and creates a summary embed with proper formatting."""
    email = None
    card_info = None
    store = "Unknown Store"
    eta = "Unknown ETA"
    order_items = []
    order_link = None  # Prevents "Click Here" bug
    name = "Unknown Name"
    address = "Unknown Address"

    if "fields" in embed_data:
        for field in embed_data["fields"]:
            field_name = field.get("name", "").strip()
            field_value = field.get("value", "").strip()

            if "Email" in field_name:
                email = field_value
            elif "Payment" in field_name:
                card_info = field_value.replace("||", "")  # Remove hidden Discord spoilers
            elif "Store" in field_name:
                store = field_value
            elif "Estimated Arrival" in field_name:
                # ✅ Extract and Convert to 12-hour format
                match = re.search(r"(\d{2}:\d{2})", field_value)
                if match:
                    eta_24h = match.group(1)
                    eta_12h = datetime.strptime(eta_24h, "%H:%M").strftime("%I:%M %p")  # Convert 24h → 12h
                    eta = eta_12h.lstrip("0")  # Remove leading zero
                else:
                    eta = field_value  # Fallback
            elif "Order Items" in field_name:
                order_items = field_value.split("\n")
            elif "Order Link" in field_name:
                # �� Ensure only the raw URL is extracted
                order_link_match = re.search(r"\((https?://[^\)]+)\)", field_value)
                order_link = order_link_match.group(1) if order_link_match else None
            elif "Name" in field_name:
                name = field_value
            elif "Delivery Address" in field_name:
                address = field_value

    # �� Correctly format the order link
    order_link_text = f"[Click Here]({order_link})" if order_link else "N/A"

    # ✅ Create an embed summary
    order_embed = discord.Embed(
        title="🎉 Order Successfully Placed!",
        description=f"**Store:** {store}\n**Estimated Arrival:** {eta}",
        color=discord.Color.green()
    )
    order_embed.add_field(name="🛒 Order Items", value="\n".join(order_items), inline=False)
    order_embed.add_field(name="📍 Delivery Address", value=address, inline=False)
    order_embed.add_field(name="👤 Customer", value=name, inline=True)
    order_embed.add_field(name="🔗 Order Link", value=order_link_text, inline=False)  # ✅ Fixed bug
    order_embed.set_footer(text="Order successfully placed!")

    return email, card_info, order_embed

async def update_order_count(channel, identifier):
    """
    Updates the order count for a given identifier in a channel.
    Returns the new count.
    """
    logging.info(f"📊 Counting orders for identifier: {identifier} in channel: {channel.name}")

    count = 0
    async for message in channel.history(limit=None):
        if identifier in message.content:
            order_match = re.search(rf"{identifier} / (\d+) order", message.content)
            if order_match:
                current_count = int(order_match.group(1))
                count = max(count, current_count)

    new_count = count + 1
    formatted_message = f"{identifier} / {new_count} order{'s' if new_count > 1 else ''}"

    # Add delay before sending message to avoid rate limits
    await asyncio.sleep(1)
    await channel.send(formatted_message)
    logging.info(f"✅ Updated count for {identifier}: {new_count} orders")

    return new_count



async def update_orders_channel(guild, new_count):
    """
    Updates the orders channel name with improved retry logic and exponential backoff
    """
    orders_channel = guild.get_channel(1341119607196483685)
    if not orders_channel:
        return

    max_retries = 5  # Increased from 3 to 5
    base_delay = 10  # Increased from 5 to 10 seconds

    for attempt in range(max_retries):
        try:
            # Check if the channel name already has the correct count
            current_count = int(orders_channel.name.split("┃")[1]) if "┃" in orders_channel.name else 0
            if current_count == new_count:
                return

            await orders_channel.edit(name=f"orders┃{new_count}")
            return
        except discord.HTTPException as e:
            if e.code == 429:  # Rate limit error
                retry_after = e.retry_after if hasattr(e, 'retry_after') else base_delay * (2 ** attempt)
                logging.warning(f"Rate limited when updating orders channel. Waiting {retry_after} seconds. Attempt {attempt + 1}/{max_retries}")
                await asyncio.sleep(retry_after)
            else:
                logging.error(f"Error updating orders channel: {str(e)}")
                return
        except Exception as e:
            logging.error(f"Unexpected error updating orders channel: {str(e)}")
            return

    logging.error(f"Failed to update orders channel after {max_retries} attempts")

async def find_latest_uber_link(channel):
    """Finds the latest Uber Eats group order link in a given channel, checking both messages and embeds."""
    async for message in channel.history(limit=50):
        # 🔍 Check the message content first
        # Updated regex pattern to handle all country code variations including us-es format
        match = re.search(r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)", message.content)
        if match:
            logging.info(f"✅ Found Uber Eats link in message: {match.group(0)}")
            return match.group(0)

        # 🔍 Check embeds (if the message has any)
        for embed in message.embeds:
            if embed.description:  # Some embeds store the link in `description`
                match = re.search(r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)", embed.description)
                if match:
                    logging.info(f"✅ Found Uber Eats link in embed description: {match.group(0)}")
                    return match.group(0)

            if embed.title:  # Some embeds store the link in `title`
                match = re.search(r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)", embed.title)
                if match:
                    logging.info(f"✅ Found Uber Eats link in embed title: {match.group(0)}")
                    return match.group(0)

            if embed.fields:  # Some embeds store links in fields
                for field in embed.fields:
                    match = re.search(r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)", field.value)
                    if match:
                        logging.info(f"✅ Found Uber Eats link in embed field: {match.group(0)}")
                        return match.group(0)

    logging.warning("❌ No Uber Eats link found in messages or embeds.")
    return None  # No valid link found


class PriceBreakdownButton(View):
    def __init__(self, summary_text):
        super().__init__(timeout=None)
        self.summary_text = summary_text

    @discord.ui.button(label="💰 Price Breakdown", style=discord.ButtonStyle.green)
    async def show_price_breakdown(self, interaction: discord.Interaction, button: Button):
        """Handles the button click and sends the price breakdown embed."""
        logging.info(f"🔘 'Price Breakdown' button clicked by {interaction.user}.")

        # Generate the price breakdown embed
        price_breakdown_embed = generate_price_breakdown_embed(self.summary_text)

        # Send the embed response
        await interaction.response.send_message(embed=price_breakdown_embed, ephemeral=True)

async def count_vouches(channel_id: int):
    """Count all images in the channel and track per-user stats"""
    headers = {
        "Authorization": TOKEN_2,
        "User-Agent": "DiscordBot (https://discord.com) Python/3.9"
    }

    url = f"https://discord.com/api/v9/channels/{channel_id}/messages?limit=100"

    logging.info("Starting vouch scan...")

    # Stats storage
    user_stats = defaultdict(int)
    total_images = 0

    while url:
        async with aiohttp.ClientSession() as session:
            async with session.get(url, headers=headers) as response:
                if response.status != 200:
                    logging.error(f"Error fetching messages: {response.status}")
                    return None, 0

                messages = await response.json()

        if not messages:
            break

        logging.info(f"Processing batch of {len(messages)} messages...")

        for message in messages:
            # Count attachments
            attachments = message.get("attachments", [])
            image_count = len([a for a in attachments
                             if a.get("filename", "").lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.webp'))])

            # Count embedded images
            embeds = message.get("embeds", [])
            embed_images = sum(1 for embed in embeds
                             if embed.get("type") == "image" or
                             (embed.get("image") and embed.get("image").get("url")))

            total = image_count + embed_images

            if total > 0:
                username = message["author"]["username"]
                user_stats[username] += total
                total_images += total

        # Get next batch of messages
        if len(messages) == 100:
            last_id = messages[-1]["id"]
            url = f"https://discord.com/api/v9/channels/{channel_id}/messages?limit=100&before={last_id}"
            logging.info(f"Fetching next batch before message ID: {last_id}")
        else:
            url = None

    logging.info(f"Scan complete. Found {total_images} total vouches")

    # Sort stats by count
    sorted_stats = sorted(user_stats.items(), key=lambda x: x[1], reverse=True)

    return sorted_stats, total_images


async def fetch_order_details(order_id: str, session: Optional[aiohttp.ClientSession] = None) -> dict:
    """Fetch order details from UberEats API."""
    url = "https://www.ubereats.com/_p/api/getActiveOrdersV1"

    logging.info(f"🔍 Attempting to fetch details for order: {order_id}")

    timeout = aiohttp.ClientTimeout(total=30, connect=10)
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    headers = {
        "accept": "*/*",
        "content-type": "application/json",
        "x-csrf-token": "x",
        "cookie": UBER_COOKIE if UBER_COOKIE else "",
        "origin": "https://www.ubereats.com",
        "referer": f"https://www.ubereats.com/orders/{order_id}"
    }

    payload = {
        "orderUuid": order_id,
        "timezone": "America/New_York"
    }

    try:
        # Use provided session or create a new one
        should_close_session = False
        if session is None:
            session = aiohttp.ClientSession(timeout=timeout)
            should_close_session = True

        try:
            async with session.post(url, json=payload, headers=headers, ssl=ssl_context) as response:
                if response.status == 200:
                    data = await response.json()
                    logging.info(f"📦 Raw API response: {data}")

                    order = data.get('data', {}).get('orders', [{}])[0]

                    # Extract store name from multiple possible locations
                    store_name = None

                    # Try store.title (most reliable)
                    store_title = order.get('store', {}).get('title')
                    if store_title:
                        store_name = store_title
                        logging.info(f"🏪 Store name from store.title: {store_name}")

                    # Try orderSummary.restaurantName
                    order_summary_name = order.get('orderSummary', {}).get('restaurantName')
                    if order_summary_name:
                        store_name = order_summary_name
                        logging.info(f"🏪 Store name from orderSummary.restaurantName: {store_name}")

                    # Try feedCards for restaurant type
                    for card in order.get('feedCards', []):
                        if card.get('type') == 'restaurant':
                            restaurant_name = card.get('title')
                            if restaurant_name:
                                store_name = restaurant_name
                                logging.info(f"🏪 Store name from feedCards: {store_name}")
                                break

                    # Try activeOrderOverview.subtitle (but filter out order summaries)
                    active_order_subtitle = order.get('activeOrderOverview', {}).get('subtitle')
                    if active_order_subtitle and not ('item' in active_order_subtitle.lower() or '$' in active_order_subtitle):
                        store_name = active_order_subtitle
                        logging.info(f"🏪 Store name from activeOrderOverview.subtitle: {store_name}")

                    # Try activeOrderOverview.title (least reliable, often contains order summary)
                    active_order_title = order.get('activeOrderOverview', {}).get('title')
                    if not store_name and active_order_title and not ('item' in active_order_title.lower() or '$' in active_order_title):
                        store_name = active_order_title
                        logging.info(f"🏪 Store name from activeOrderOverview.title: {store_name}")

                    # Fallback
                    if not store_name:
                        store_name = 'Unknown Store'
                        logging.warning("Could not find store name in API response")

                    logging.info(f"🏪 Final store name: {store_name}")

                    # Extract ETA from activeOrderStatus
                    eta = order.get('activeOrderStatus', {}).get('title') or \
                          order.get('inAppNotification', {}).get('title')
                    logging.info(f"🕒 ETA: {eta}")

                    # Extract items from orderSummary
                    items = []
                    active_order = order.get('activeOrderOverview', {})
                    order_items = active_order.get('items', [])

                    for item in order_items:
                        quantity = item.get('quantity', 1)
                        name = item.get('title', 'Unknown Item')
                        subtitle = item.get('subtitle', '')
                        items.append(f"• {quantity}x {name}\n  {subtitle}")

                    # If no items found, try orderSummary as fallback
                    if not items:
                        order_summary = order.get('orderSummary', {})
                        sections = order_summary.get('sections', [])

                        for section in sections:
                            section_items = section.get('items', [])
                            for item in section_items:
                                quantity = item.get('quantity', 1)
                                name = item.get('title', 'Unknown Item')
                                subtitle = item.get('subtitle', '')
                                items.append(f"• {quantity}x {name}\n  {subtitle}")

                    # Extract delivery address from feedCards
                    address = None
                    for card in order.get('feedCards', []):
                        if card.get('type') == 'delivery':
                            delivery_details = card.get('delivery', {})
                            address = delivery_details.get('formattedAddress')
                            break
                    logging.info(f"📍 Delivery address: {address}")

                    # Extract customer info from orderInfo
                    customer_info = order.get('orderInfo', {}).get('customerInfos', [{}])[0]
                    first_name = customer_info.get('firstName', '')
                    last_name = customer_info.get('lastName', '')
                    customer_name = f"{first_name} {last_name}".strip()
                    logging.info(f"👤 Customer name: {customer_name}")

                    result = {
                        'store': store_name or 'N/A',
                        'eta': eta or 'N/A',
                        'items': '\n'.join(items) or 'No items found',
                        'address': address or 'N/A',
                        'customer': customer_name or 'N/A'
                    }

                    logging.info(f"✅ Final processed data: {result}")
                    return result
                else:
                    logging.error(f"❌ API request failed with status {response.status}")
                    return None
        finally:
            # Close the session if we created it
            if should_close_session and session and not session.closed:
                await session.close()
    except Exception as e:
        logging.error(f"❌ Error fetching order details: {str(e)}")
        return None

def generate_price_breakdown_embed(summary_text, fixed_discount_amount=20.00):
    """Processes the latest order summary and generates a price breakdown embed."""
    if not summary_text:
        return discord.Embed(
            title="❌ Error",
            description="No valid order summary found.",
            color=discord.Color.red()
        )

    # Detect currency
    currency_match = re.search(r"(CA\$|\$)", summary_text)
    currency_symbol = currency_match.group(1) if currency_match else "$"
    is_cad = currency_symbol == "CA$"
    escaped_currency = re.escape(currency_symbol)

    # Extract values using regex
    subtotal_match = re.search(rf"Subtotal:\s*{escaped_currency}([\d,]+\.\d+)", summary_text)
    delivery_match = re.search(rf"Delivery Fee:\s*{escaped_currency}([\d,]+\.\d+)", summary_text)
    taxes_match = re.search(rf"(?:Taxes & Other l|Fees):\s*{escaped_currency}([\d,]+\.\d+)", summary_text)

    # Convert extracted values to float
    subtotal = float(subtotal_match.group(1).replace(',', '')) if subtotal_match else 0.0
    delivery_fee = float(delivery_match.group(1).replace(',', '')) if delivery_match else 0.0
    taxes_fees = float(taxes_match.group(1).replace(',', '')) if taxes_match else 0.0

    # Create the embed
    embed = discord.Embed(
        title="💰 Order Price Breakdown",
        color=discord.Color.green()
    )

    # Calculate components based on fixed discount amount
    discount_amount = 26.00 if is_cad else fixed_discount_amount  # CAD gets $26 discount
    threshold = 30 if is_cad else 25
    overflow_fee = round(subtotal - threshold, 2) if subtotal > threshold else 0
    discounted_subtotal = max(0, subtotal - discount_amount)
    final_total = round(discounted_subtotal + overflow_fee + taxes_fees + delivery_fee, 2)

    # Add fields to embed
    embed.add_field(name="🧾 Subtotal", value=f"{currency_symbol}{subtotal:.2f}", inline=False)
    embed.add_field(name=f"💰 Fixed Discount", value=f"-{currency_symbol}{discount_amount:.2f}", inline=False)
    if overflow_fee > 0:
        embed.add_field(name="⚠️ Overflow Fee", value=f"+{currency_symbol}{overflow_fee:.2f}", inline=False)
    embed.add_field(name="📦 Delivery Fee", value=f"{currency_symbol}{delivery_fee:.2f}", inline=False)
    embed.add_field(name="💰 Fees", value=f"{currency_symbol}{taxes_fees:.2f}", inline=False)
    embed.add_field(name="💵 Final Total", value=f"{currency_symbol}{final_total:.2f}", inline=False)

    return embed

class PaymentMethodButtons(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="Cerv", style=discord.ButtonStyle.primary)
    async def cerv_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        logging.info(f"🔘 'Cerv Payment Methods' button clicked by {interaction.user} ({interaction.user.id})")
        await cerv(interaction)

    @discord.ui.button(label="Nelo", style=discord.ButtonStyle.success)
    async def nelo_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        logging.info(f"🔘 'Nelo's Payment Methods' button clicked by {interaction.user} ({interaction.user.id})")
        await nelo(interaction)

    @discord.ui.button(label="Glitch", style=discord.ButtonStyle.danger)
    async def glitchyz_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        logging.info(f"🔘 'Glitch's Payment Methods' button clicked by {interaction.user} ({interaction.user.id})")
        await Glitchyz(interaction)


class VouchPaginator(discord.ui.View):
    def __init__(self, sorted_stats, total_images):
        super().__init__(timeout=None)
        self.sorted_stats = sorted_stats
        self.total_images = total_images
        self.current_page = 0
        self.items_per_page = 10
        self.total_pages = (len(sorted_stats) - 1) // self.items_per_page + 1

    def get_embed(self):
        start_idx = self.current_page * self.items_per_page
        end_idx = start_idx + self.items_per_page
        current_chunk = self.sorted_stats[start_idx:end_idx]

        embed = discord.Embed(
            title="📊 Vouch Statistics",
            description=f"**TOTAL VOUCHES: {self.total_images}**\nPage {self.current_page + 1}/{self.total_pages}",
            color=discord.Color.blue()
        )

        for i, (username, count) in enumerate(current_chunk, start_idx + 1):
            embed.add_field(
                name=f"#{i} {username}",
                value=f"{count} vouch{'es' if count != 1 else ''}",
                inline=True
            )

        return embed

    @discord.ui.button(label="Previous", style=discord.ButtonStyle.gray)
    async def previous(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page > 0:
            self.current_page -= 1
            await interaction.response.edit_message(embed=self.get_embed(), view=self)
        else:
            await interaction.response.defer()

    @discord.ui.button(label="Next", style=discord.ButtonStyle.gray)
    async def next(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            await interaction.response.edit_message(embed=self.get_embed(), view=self)
        else:
            await interaction.response.defer()

def format_cart_items(raw_cart_items):
    """Helper function to format cart items with customizations"""
    formatted_items = []
    for item in raw_cart_items:
        base_price = float(item.get('price', 0))
        quantity = int(item.get('quantity', 1))
        title = item.get('title', 'Unknown Item')

        # Process customizations
        customizations = item.get('customizations', {})
        addon_price = 0
        addon_names = []

        # Iterate through all customization groups
        for group_key, customization_list in customizations.items():
            for customization in customization_list:
                price = float(customization.get('price', 0))
                if price > 0:  # Only include paid add-ons
                    addon_price += price
                    addon_names.append(customization.get('title', ''))

        # Calculate total item price (base + addons) in dollars
        total_item_price = (base_price + addon_price) / 100

        # Format the item string with addons if present
        item_text = title
        if addon_names:
            paid_addons = [name for name in addon_names if name]
            if paid_addons:
                item_text += f" ({', '.join(paid_addons)})"

        formatted_items.append(f"╰・x{quantity} {item_text} (${total_item_price:.2f})")

    return formatted_items

async def latestsummary(
    interaction: discord.Interaction,
    subtotal: float,
    fees: float,
    grouporderlink: str = None
):
    try:
        logging.info(f"🚀 LATESTSUMMARY COMMAND START")
        logging.info(f"👤 Triggered by: {interaction.user} (ID: {interaction.user.id})")
        logging.info(f"💲 Input - Subtotal: ${subtotal:.2f}, Fees: ${fees:.2f}")
        logging.info(f"🔗 Group Order Link: {grouporderlink}")

        await interaction.response.defer()
        logging.info("⏳ Interaction deferred")

        # Initialize variables
        location = "Location not provided"
        cart_items = []

        if grouporderlink:
            logging.info(f"🔄 Processing group order link...")
            result = await process_group_order(grouporderlink)

            if result:
                # Ensure the group link is included in the result
                result['group_link'] = grouporderlink  # Add this line
                logging.info("✅ Group order processed successfully")

                loc = result.get('location', {})
                if loc:
                    location = f"{loc.get('address', 'Unknown')}, {loc.get('city', 'Unknown')}, {loc.get('state', 'XX').upper()}"
                    logging.info(f"📍 Location extracted: {location}")

        # If no group order link provided, search in chat history
        if not grouporderlink:
            logging.info("🔍 No direct group order link - Searching channel history...")
            message_count = 0
            found_link = False

            async for message in interaction.channel.history(limit=50):
                message_count += 1
                logging.info(f"📝 Checking message {message_count}")

                pattern = r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)"

                match = re.search(pattern, message.content)
                if match:
                    grouporderlink = match.group(0)
                    logging.info(f"✅ Found group order link: {grouporderlink}")
                    found_link = True
                    break

                for embed in message.embeds:
                    if embed.description:
                        match = re.search(pattern, embed.description)
                        if match:
                            grouporderlink = match.group(0)
                            logging.info(f"✅ Found group order link in embed: {grouporderlink}")
                            found_link = True
                            break

                if found_link:
                    break

        # Process group order if we have a link
        if grouporderlink:
            logging.info(f"🔄 Processing group order link...")
            result = await process_group_order(grouporderlink)

            if result:
                logging.info("✅ Group order processed successfully")

                loc = result.get('location', {})
                if loc:
                    location = f"{loc.get('address', 'Unknown')}, {loc.get('city', 'Unknown')}, {loc.get('state', 'XX').upper()}"
                    logging.info(f"📍 Location extracted: {location}")

                # Format cart items from dictionaries to strings
                raw_cart_items = result.get('cart_items', [])
                cart_items = []
                if raw_cart_items:
                    cart_items = format_cart_items(raw_cart_items)
                    logging.info(f"🛒 Retrieved {len(cart_items)} cart items")
                else:
                    logging.warning("⚠️ No cart items in result")

        # Calculate totals
        logging.info("💰 Calculating totals...")
        fixed_discount = 20.00  # Fixed $20 discount
        discounted_subtotal = max(0, subtotal - fixed_discount)
        savings = round(subtotal - discounted_subtotal, 2)
        final_total = round(discounted_subtotal + fees, 2)

        logging.info(f"📊 Calculations complete:")
        logging.info(f"   Original: ${subtotal:.2f}")
        logging.info(f"   Discounted: ${discounted_subtotal:.2f}")
        logging.info(f"   Savings: ${savings:.2f}")
        logging.info(f"   Fees: ${fees:.2f}")
        logging.info(f"   Final: ${final_total:.2f}")

        # Create embed with enhanced modern design
        embed = discord.Embed(
            title="🍽️ Order Summary",
            description="Your Uber Eats order has been processed with The Method discount.",
            color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
        )

        # Set the same thumbnail
        embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/29FBokwLD5znXStCH2FvFcSsw-XO__J1aUZLmnpI5NI/https/i.gyazo.com/5dbb6a00ae9f91f522331b11d0053e86.gif?width=72&height=72")

        # Add a clean divider
        embed.add_field(
            name="",
            value="━━━━━━━━━━━━━━━━━━━━━━━━━",
            inline=False
        )

        # Add location with better formatting
        embed.add_field(name="📍 Delivery Location", value=f"```{location}```", inline=False)

        # Add cart items with better formatting
        if cart_items:
            formatted_items = [f"• └── {item}" for item in cart_items]
            embed.add_field(name="🛒 Order Items", value="\n".join(formatted_items), inline=False)

            # Add a clean divider
            embed.add_field(
                name="",
                value="━━━━━━━━━━━━━━━━━━━━━━━━━",
                inline=False
            )

            # Price breakdown section with better formatting
            embed.add_field(
                name="💰 Price Breakdown",
                value=f"**Original Subtotal:** `${subtotal:.2f}`\n" +
                      f"**Discounted (70% OFF):** `${discounted_subtotal:.2f}`\n" +
                      f"**You Save:** `${savings:.2f}`",
                inline=False
            )

            # Fees section with better formatting
            embed.add_field(
                name="📊 Fees & Taxes",
                value=f"**Total Fees:** `${fees:.2f}`",
                inline=False
            )

            # Final total with emphasis
            embed.add_field(
                name="🧾 Final Total",
                value=f"**`${final_total:.2f}`** *(tip not included)*",
                inline=False
            )

            # Add group order link if available
            if grouporderlink:
                embed.add_field(
                    name="🔗 Group Order Link",
                    value=f"[**Click to view order**]({grouporderlink})",
                    inline=False
                )

            # Add timestamp
            embed.timestamp = datetime.now()

        # Create view with enhanced payment methods button
        combined_view = discord.ui.View(timeout=None)

        # Payment methods button with improved styling
        payment_methods_button = discord.ui.Button(
            label="💳 View Payment Methods",
            style=discord.ButtonStyle.primary,  # Discord blue color
            custom_id="payment_methods"
        )
        payment_methods_button.callback = lambda i: payment_methods_callback(i)
        combined_view.add_item(payment_methods_button)

        # Add a footer with helpful information
        embed.set_footer(text="Use the button below to view payment options | The Method")

        await interaction.followup.send(
            embed=embed,
            view=combined_view
        )
        logging.info("✅ LATESTSUMMARY COMMAND COMPLETE")

    except Exception as e:
        logging.error(f"❌ Error in latestsummary: {str(e)}")
        logging.error(f"📚 Full traceback:\n{traceback.format_exc()}")
        await interaction.followup.send(
            "❌ An error occurred while processing the summary.",
            ephemeral=True
        )

async def start_group_order_action(url: str) -> bool:
    """Start the group order action."""
    try:
        # Extract UUID from the URL
        uuid_match = re.search(r'group-orders/([a-zA-Z0-9-]+)', url)
        if not uuid_match:
            logging.error("Failed to extract UUID from group order URL")
            return False

        group_uuid = uuid_match.group(1)

        # Start the action
        async with aiohttp.ClientSession() as session:
            payload = {
                "action": "start_group_order",
                "uuid": group_uuid
            }

            headers = {
                "Content-Type": "application/json"
            }

            async with session.post(
                "http://localhost:8080/api/action",  # Adjust the port if needed
                json=payload,
                headers=headers
            ) as response:
                if response.status == 200:
                    logging.info("✅ Successfully started group order action")
                    return True
                else:
                    logging.error(f"❌ Failed to start action. Status: {response.status}")
                    return False

    except Exception as e:
        logging.error(f"Error starting group order action: {str(e)}")
        return False

async def show_price_breakdown(interaction: discord.Interaction, summary_text: str, discount: int = 70):
    """Callback for the Price Breakdown button"""
    logging.info(f"🔘 'Price Breakdown' button clicked by {interaction.user} ({interaction.user.id})")
    price_breakdown_embed = generate_price_breakdown_embed(summary_text, discount)
    await interaction.response.send_message(embed=price_breakdown_embed, ephemeral=True)

async def payment_methods_callback(interaction: discord.Interaction):
    """Callback for the Payment Methods button"""
    logging.info(f"🔘 'Payment Methods' button clicked by {interaction.user} ({interaction.user.id})")
    embed = discord.Embed(
        title="Who is doing your order?",
        description="Select the person handling your order to see their payment methods:",
        color=discord.Color.blue()
    )
    await interaction.response.send_message(
        embed=embed,
        view=PaymentMethodButtons()
    )

class CervPaymentButtons(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

    @discord.ui.button(label="📋 Copy Zelle Info", style=discord.ButtonStyle.secondary)
    async def copy_zelle_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        """Handles the copy Zelle info button click."""
        logging.info(f"🔘 'Copy Zelle Info' button clicked by {interaction.user} ({interaction.user.id})")

        # Send just the Zelle phone number as plain text in the channel (non-ephemeral)
        await interaction.response.send_message("**********")

async def cerv(interaction: discord.Interaction):
    """Sends Cerv's payment details as an embed."""
    embed = discord.Embed(
        title="💰 Cerv's Payment Methods",
        description="Select your preferred payment option below:",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Digital payments (recommended)
    embed.add_field(
        name="💳 Digital payments (recommended)",
        value="**Zelle:** `**********`\n**Venmo:** `@CervMethod` *(F&F)*\n**PayPal:** `@ItsCerv` *(F&F)*",
        inline=False
    )

    # Cryptocurrency
    embed.add_field(
        name="🪙 Cryptocurrency",
        value="ask for crypto",
        inline=False
    )

    # Card payments (credits only)
    embed.add_field(
        name="🏦 Card payments (credits only)",
        value="[**Pay with Card/Cashapp/Bank**](https://themethod.mysellauth.com/product/credits-cerv)",
        inline=False
    )

    # Add footer with important note
    embed.set_footer(text="⚠️ IMPORTANT: Always use Friends & Family (F&F) for PayPal/Venmo payments")

    # Create view with copy button
    view = CervPaymentButtons()

    # Send as public message (visible to everyone in the channel) with interactive buttons
    await interaction.response.send_message(embed=embed, view=view)

async def nelo(interaction: discord.Interaction):
    """Sends Nelo's payment details as an embed."""
    embed = discord.Embed(
        title="💰 Nelo's Payment Methods",
        description="Select your preferred payment option below:",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add a thumbnail for a modern look
    embed.set_thumbnail(url="https://i.imgur.com/8tJQj5Y.png")  # Payment icon

    # Card/Digital Payments (grouped)
    embed.add_field(
        name="💳 Card & Digital Payments",
        value="[**Pay with Card/CashApp/Klarna/Link/Google Pay/Amazon Pay/Apple Pay**](https://buy.stripe.com/00g8zi2IH2HbfpSdQQ)",
        inline=False
    )

    # Bank Transfers (grouped)
    embed.add_field(
        name="🏦 Bank Transfers",
        value="**Zelle:** `**********`\n**Venmo:** `@Howard-Chen-75` *(F&F)*",
        inline=False
    )

    # PayPal
    embed.add_field(
        name="💳 PayPal",
        value="**Username:** `@HowardChen754` *(F&F)*\n[**PayPal.me Link**](https://paypal.me/HowardChen754)",
        inline=False
    )

    # Set a modern thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/8tJQj5Y.png")  # Payment icon

    # Add footer with important note
    embed.set_footer(text="⚠️ IMPORTANT: Always use Friends & Family (F&F) for PayPal/Venmo payments")

    # Send as public message (visible to everyone in the channel)
    await interaction.response.send_message(embed=embed)

async def Glitchyz(interaction: discord.Interaction):
    """Sends Glitchyz's payment details as an embed."""
    embed = discord.Embed(
        title="💰 Glitchyz's Payment Methods",
        description="Select your preferred payment option below:",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add a thumbnail for a modern look
    embed.set_thumbnail(url="https://i.imgur.com/8tJQj5Y.png")  # Payment icon

    # Bank Transfers (grouped)
    embed.add_field(
        name="🏦 Bank Transfers",
        value="**Zelle:** `**********`\n**Venmo:** `@GlitchUE` *(F&F)*",
        inline=False
    )

    # PayPal
    embed.add_field(
        name="💳 PayPal",
        value="**Username:** `@GlitchUE` *(F&F)*\n[**PayPal.me Link**](https://paypal.me/GlitchUE)",
        inline=False
    )

    # Set a modern thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/8tJQj5Y.png")  # Payment icon

    # Add footer with important note
    embed.set_footer(text="⚠️ IMPORTANT: Always use Friends & Family (F&F) for PayPal/Venmo payments")

    # Send as public message (visible to everyone in the channel)
    await interaction.response.send_message(embed=embed)

async def dessie(interaction: discord.Interaction):
    """Sends Dessie's payment details as an embed."""
    embed = discord.Embed(
        title="💰 Dessie's Payment Methods",
        description="Select your preferred payment option below:",
        color=discord.Color.from_rgb(91, 141, 238)  # Light Blue
    )

    # Removed divider

    # Digital Payments
    embed.add_field(
        name="💵 CashApp",
        value="**Username:** `$vvzxsheins`",
        inline=False
    )

    # PayPal
    embed.add_field(
        name="💳 PayPal",
        value="**Username:** `@dessievuh` *(F&F)*",
        inline=False
    )

    # Set a modern thumbnail
    embed.set_thumbnail(url="https://i.imgur.com/8tJQj5Y.png")  # Payment icon

    # Add footer with important note
    embed.set_footer(text="⚠️ IMPORTANT: Always use Friends & Family (F&F) for PayPal payments")

    # Send as public message (visible to everyone in the channel)
    await interaction.response.send_message(embed=embed)

class OrderHelpButtons(discord.ui.View):
    def __init__(self):
        super().__init__(timeout=None)

        # Add a URL button that directly links to the how-to-order channel
        self.add_item(discord.ui.Button(
            style=discord.ButtonStyle.primary,
            label="How to Order",
            emoji="📋",
            url=f"https://discord.com/channels/{os.getenv('DISCORD_GUILD_ID')}/1340210714891128882"
        ))

async def open_store(interaction: discord.Interaction):
    """Opens the store."""
    await interaction.response.defer(ephemeral=True)

    guild = interaction.guild
    status_channel = guild.get_channel(1340406893927075900)  # Status Channel
    store_channel = guild.get_channel(1340210714891128882)  # Store channel
    customer_role = guild.get_role(1343166726170345523)  # Customer role
    ping_role = guild.get_role(1340825948890726511)  # Role to ping
    verified_channel = guild.get_channel(1340194718637625397)  # Verified Channel
    orders_channel = guild.get_channel(1340392172691918848)  # Orders Channel

    if not all([status_channel, store_channel, customer_role, verified_channel, orders_channel]):
        await interaction.followup.send("❌ Error: Required channels or roles not found.", ephemeral=True)
        return

    # Change channel name to open status
    await status_channel.edit(name="🟢┃status-open")

    # Update store channel permissions for the customer role
    overwrite = store_channel.overwrites_for(customer_role)
    overwrite.view_channel = True
    await store_channel.set_permissions(customer_role, overwrite=overwrite)

    # Update verified channel permissions (show when open)
    verified_overwrite = verified_channel.overwrites_for(customer_role)
    verified_overwrite.view_channel = True
    await verified_channel.set_permissions(customer_role, overwrite=verified_overwrite)

    # Update orders channel permissions (can type when open)
    orders_overwrite = orders_channel.overwrites_for(customer_role)
    orders_overwrite.send_messages = True
    orders_overwrite.view_channel = True
    await orders_channel.set_permissions(customer_role, overwrite=orders_overwrite)

    # Send the @hungry role ping as a separate message
    if ping_role:
        await status_channel.send(f"{ping_role.mention} Open for orders")

    # Create embed with enhanced design
    embed = discord.Embed(
        title="<:startup:1360177289664401418> The Method is Now Open!",
        description="**We are now accepting orders!** Place your order using the instructions below.",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add some spacing between title and content
    embed.description += "\n"

    # Set image and footer
    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1344614541195218975/a.jpg?ex=67c18d60&is=67c03be0&hm=31b0756c3bb9d314fecab34039c85025b4271a349a8d5f9a7267a4c74e9864bc&=&format=webp&width=947&height=541")
    embed.set_footer(text="The Method | Fast & Reliable Service")

    # Create view with helpful buttons
    view = OrderHelpButtons()

    # Implement persistent embed system
    embed_sent = False
    try:
        # Import status embed data from themethodbot
        from themethodbot.themethodbot import status_embed_data, save_status_embed_data
        logging.info("Successfully imported status embed data")

        # Try to edit existing message
        stored_message_id = status_embed_data.get('message_id')
        logging.info(f"Stored message ID: {stored_message_id}")

        if stored_message_id:
            try:
                message = await status_channel.fetch_message(stored_message_id)
                await message.edit(embed=embed, view=view)
                logging.info(f"✅ Updated existing status embed message ID: {stored_message_id}")
                embed_sent = True
            except discord.NotFound:
                # Message was deleted, send new one and store new ID
                new_message = await status_channel.send(embed=embed, view=view)
                status_embed_data['message_id'] = new_message.id
                await save_status_embed_data()
                logging.info(f"✅ Status message not found, created new one with ID: {new_message.id}")
                embed_sent = True
            except Exception as e:
                logging.error(f"❌ Error editing status embed: {e}")
                # Fallback to sending new message
                new_message = await status_channel.send(embed=embed, view=view)
                status_embed_data['message_id'] = new_message.id
                await save_status_embed_data()
                logging.info(f"✅ Fallback: Created new status embed with ID: {new_message.id}")
                embed_sent = True
        else:
            # No stored message ID, send new message
            new_message = await status_channel.send(embed=embed, view=view)
            status_embed_data['message_id'] = new_message.id
            await save_status_embed_data()
            logging.info(f"✅ No stored message ID, created new status embed with ID: {new_message.id}")
            embed_sent = True
    except ImportError as e:
        # Fallback if import fails - send new message
        logging.error(f"❌ Could not import status embed data: {e}")
        new_message = await status_channel.send(embed=embed, view=view)
        logging.info(f"✅ Fallback: Sent new embed message with ID: {new_message.id}")
        embed_sent = True
    except Exception as e:
        logging.error(f"❌ Unexpected error in persistent embed system: {e}")
        if not embed_sent:
            new_message = await status_channel.send(embed=embed, view=view)
            logging.info(f"✅ Emergency fallback: Sent new embed message with ID: {new_message.id}")
            embed_sent = True

    # Final safety check - ensure embed was sent
    if not embed_sent:
        logging.error("❌ CRITICAL: Embed was not sent, sending emergency fallback")
        await status_channel.send(embed=embed, view=view)

    # Send confirmation to user
    await interaction.followup.send("✅ Service has been opened successfully!", ephemeral=True)

async def close_store(interaction: discord.Interaction):
    """Slash command to close the store."""
    await interaction.response.defer(ephemeral=True)

    guild = interaction.guild
    status_channel = guild.get_channel(1340406893927075900)  # Status Channel
    store_channel = guild.get_channel(1340210714891128882)  # Store Channel
    customer_role = guild.get_role(1343166726170345523)  # Customer role
    verified_channel = guild.get_channel(1340194718637625397)  # Verified Channel
    orders_channel = guild.get_channel(1340392172691918848)  # Orders Channel

    if not all([status_channel, store_channel, customer_role, verified_channel, orders_channel]):
        await interaction.followup.send("❌ Error: Required channels or roles not found.", ephemeral=True)
        return

    # Change status channel name to indicate closed status
    await status_channel.edit(name="🔴┃status")

    # Update store channel permissions for the customer role
    overwrite = store_channel.overwrites_for(customer_role)
    overwrite.view_channel = False
    await store_channel.set_permissions(customer_role, overwrite=overwrite)

    # Update verified channel permissions (hide when closed)
    verified_overwrite = verified_channel.overwrites_for(customer_role)
    verified_overwrite.view_channel = False
    await verified_channel.set_permissions(customer_role, overwrite=verified_overwrite)

    # Update orders channel permissions (can't type when closed)
    orders_overwrite = orders_channel.overwrites_for(customer_role)
    orders_overwrite.send_messages = False
    orders_overwrite.view_channel = True  # Keep visible but can't type
    await orders_channel.set_permissions(customer_role, overwrite=orders_overwrite)

    # Create and send embed to STATUS CHANNEL with enhanced design
    embed = discord.Embed(
        title="🔴 The Method is Now Closed!",
        description="**We are curre ntly closed.** Please check back later for updates.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add some spacing between title and content
    embed.description += "\n"

    # Set image and footer
    embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1344614540758749245/ClosedBanner.jpg?ex=67fa3de0&is=67f8ec60&hm=b74d7be3b974fc13fa60f06fa6f241dc9f02816a50ac51dff4809dbfa5da0ecb&=&format=webp&width=1466&height=838")
    embed.set_footer(text="The Method | Currently Closed")

    # Send the embed to status channel
    await status_channel.send(embed=embed)

    # Send confirmation to user
    await interaction.followup.send("✅ Service has been closed successfully!", ephemeral=True)

async def track(interaction: discord.Interaction, order_id: str):
    try:
        await interaction.response.defer(ephemeral=True)
        logging.info(f"📌 `/track` command triggered by {interaction.user} for order: {order_id}")

        # Send confirmation to user
        await interaction.followup.send("✅ Started tracking order!", ephemeral=True)

        # Start tracking in background
        asyncio.create_task(track_order_status(interaction, order_id, interaction.channel))

    except Exception as e:
        logging.error(f"❌ Error in track command: {str(e)}")
        await interaction.followup.send(
            "❌ An error occurred while processing the command.",
            ephemeral=True
        )

async def track_order_status(order_id: str, channel, session: Optional[aiohttp.ClientSession] = None, bot_name: str = None, active_tracking_dict: Dict = None, save_tracking_func = None):
    """Tracks order status and sends updates via embeds."""
    try:
        order_id_match = re.search(r"orders/([a-f0-9-]+)", order_id)
        if order_id_match:
            order_id = order_id_match.group(1)

        url = "https://www.ubereats.com/_p/api/getActiveOrdersV1"
        last_status = None
        tracking = True

        # Use provided session or create a new one
        should_close_session = False
        if session is None:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            session = aiohttp.ClientSession(timeout=timeout)
            should_close_session = True

        # Create SSL context
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        try:
            # Initial check for already delivered orders
            headers = {
                "accept": "*/*",
                "content-type": "application/json",
                "x-csrf-token": "x",
                "cookie": UBER_COOKIE if UBER_COOKIE else "",
                "origin": "https://www.ubereats.com",
                "referer": f"https://www.ubereats.com/orders/{order_id}"
            }

            payload = {
                "orderUuid": order_id,
                "timezone": "America/New_York"
            }

            # Check if order is already delivered before starting tracking loop
            try:
                async with session.post(url, json=payload, headers=headers, ssl=ssl_context) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data and "data" in data and "activeOrders" in data["data"]:
                            for order in data["data"]["activeOrders"]:
                                if order.get("uuid") == order_id:
                                    order_phase = order.get("orderPhase", "")
                                    if order_phase == "COMPLETED":
                                        logging.info(f"🎯 Order {order_id} is already delivered - stopping tracking immediately")
                                        # Remove from active tracking if already delivered
                                        if active_tracking_dict and order_id in active_tracking_dict:
                                            del active_tracking_dict[order_id]
                                            if save_tracking_func:
                                                asyncio.create_task(save_tracking_func())
                                        return  # Exit tracking function immediately
                                    break
            except Exception as e:
                logging.warning(f"Could not check initial order status for {order_id}: {e}")

            while tracking:
                try:
                    headers = {
                        "accept": "*/*",
                        "content-type": "application/json",
                        "x-csrf-token": "x",
                        "cookie": UBER_COOKIE if UBER_COOKIE else "",
                        "origin": "https://www.ubereats.com",
                        "referer": f"https://www.ubereats.com/orders/{order_id}"
                    }

                    payload = {
                        "orderUuid": order_id,
                        "timezone": "America/New_York"
                    }

                    # Add retry logic
                    for retry in range(3):
                        try:
                            async with session.post(url, json=payload, headers=headers, ssl=ssl_context) as response:
                                if response.status == 200:
                                    data = await response.json()

                                    if not data.get('data', {}).get('orders'):
                                        await channel.send(content="❌ Order not found")
                                        return

                                    order = data['data']['orders'][0]
                                    analytics = order.get('analytics', {}).get('data', {})
                                    order_status = analytics.get('order_status')
                                    order_phase = order.get('orderInfo', {}).get('orderPhase')

                                    if order_status != last_status or (order_phase == "COMPLETED" and tracking):
                                        last_status = order_status

                                        # Update tracking status using passed parameters
                                        tracking_data = None
                                        if active_tracking_dict and order_id in active_tracking_dict:
                                            tracking_data = active_tracking_dict[order_id]
                                            tracking_data['last_status'] = order_status

                                            # Add status to history
                                            status_display = get_status_display_text(order_status)
                                            tracking_data['status_history'] = add_status_to_history(
                                                tracking_data.get('status_history', []),
                                                status_display
                                            )

                                            # Save tracking data using passed function
                                            if save_tracking_func:
                                                asyncio.create_task(save_tracking_func())

                                        # Create embed with different styling based on bot_name
                                        embed = discord.Embed(
                                            title="<:car:1360177292730568865> Order Tracking",
                                            color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
                                        )

                                        # Check if this is themethodbot based ONLY on the bot_name parameter
                                        # Default to themethodbot if no bot_name is provided
                                        is_themethodbot = not (bot_name and bot_name.lower() == 'budgetbot')

                                        if not is_themethodbot:
                                            # Add a clean divider at the top for budgetbot
                                            embed.add_field(
                                                name="",
                                                value="━━━━━━━━━━━━━━━━━━━━━━━━━",
                                                inline=False
                                            )

                                        if order_phase == "COMPLETED":
                                            embed.title = "🎉 Order Delivered!"
                                            embed.description = "**Order Complete! Enjoy your meal!**\n\nThanks for choosing The Method 😈"
                                            embed.color = discord.Color.from_rgb(87, 242, 135)  # Vibrant Green

                                            # Add a timestamp
                                            embed.timestamp = datetime.now()
                                            embed.set_image(url=random.choice(COMPLETION_GIFS))
                                            tracking = False

                                            # Remove from active tracking when delivered
                                            if active_tracking_dict and order_id in active_tracking_dict:
                                                logging.info(f"🎯 Order {order_id} delivered - removing from active tracking")
                                                del active_tracking_dict[order_id]
                                                if save_tracking_func:
                                                    asyncio.create_task(save_tracking_func())

                                            class CloseTicketButton(discord.ui.Button):
                                                def __init__(self):
                                                    super().__init__(
                                                        label="Close Ticket",
                                                        style=discord.ButtonStyle.danger,
                                                        custom_id="close_ticket_button"
                                                    )

                                                async def callback(self, button_interaction: discord.Interaction):
                                                    try:
                                                        await button_interaction.response.defer()

                                                        # Send commands with TOKEN_2
                                                        url = f"https://discord.com/api/v9/channels/{channel.id}/messages"
                                                        headers = {"Authorization": TOKEN_2, "Content-Type": "application/json"}

                                                        async with aiohttp.ClientSession() as new_session:
                                                            # Send $close
                                                            async with new_session.post(url, json={"content": "$close"}, headers=headers) as response:
                                                                if response.status != 200:
                                                                    logging.error(f"Failed to send $close command: {response.status}")
                                                            await asyncio.sleep(0.5)

                                                            # Send $transcript
                                                            async with new_session.post(url, json={"content": "$transcript"}, headers=headers) as response:
                                                                if response.status != 200:
                                                                    logging.error(f"Failed to send $transcript command: {response.status}")
                                                            await asyncio.sleep(0.5)

                                                            # Send delete
                                                            async with new_session.post(url, json={"content": "$delete"}, headers=headers) as response:
                                                                if response.status != 200:
                                                                    logging.error(f"Failed to send delete command: {response.status}")

                                                        await button_interaction.followup.send("✅ Ticket closing commands sent!", ephemeral=True)

                                                    except Exception as e:
                                                        logging.error(f"Error in close ticket button: {str(e)}")
                                                        await button_interaction.followup.send("❌ Failed to close ticket", ephemeral=True)

                                            # Create persistent view
                                            class TicketView(discord.ui.View):
                                                def __init__(self):
                                                    super().__init__(timeout=None)  # Make the view persistent
                                                    self.add_item(CloseTicketButton())

                                            # Create view and add button
                                            view = TicketView()

                                            # Send completion message with embed and button
                                            await channel.send(embed=embed, view=view)

                                            # Add 3 second delay
                                            await asyncio.sleep(3)

                                            # Instead of using the API, send the completion message directly through the bot
                                            # This ensures the correct bot sends the message

                                            # Determine which bot is running based ONLY on the bot_name parameter
                                            if bot_name and bot_name.lower() == 'budgetbot':
                                                # Use budgetbot's completion message - SWAPPED AS REQUESTED
                                                completion_message = "<@1348156296704032929> Enjoy your order! could u please vouch for us 😄 <#1348113337195434066>, btw when you reach 10 vouches you get the loyal customer role, check <#1348113029778243675> for more info!"
                                                logging.info("Using budgetbot completion message (with themethodbot channels)")
                                            else:
                                                # Use themethodbot's completion message - SWAPPED AS REQUESTED
                                                completion_message = "Enjoy your order! could u please vouch for us :D <#1340392172691918848>, btw when you reach 10 vouches you get the loyal customer role, check <#1340838320976035840> for more info! <@&1340415428698308749>"
                                                logging.info("Using themethodbot completion message (with budgetbot channels)")

                                            # Send the message directly through the channel
                                            try:
                                                await channel.send(completion_message)
                                                logging.info(f"Sent completion message for {bot_name if bot_name else 'unknown bot'}")
                                            except Exception as e:
                                                logging.error(f"Error sending completion message: {str(e)}")

                                            # Rename the channel
                                            try:
                                                current_channel = channel
                                                ticket_number = current_channel.name.split("-")[0]
                                                new_name = f"{ticket_number}-delivered"

                                                await current_channel.edit(name=new_name)
                                                logging.info(f"✅ Successfully renamed channel to '{new_name}'")
                                            except Exception as e:
                                                logging.error(f"❌ Failed to rename channel: {str(e)}")

                                            break

                                        elif order_status == "EnrouteToRestaurant":
                                            embed.description = "**Driver is heading to the Store** 🚗\n\nYour order is being processed and a driver has been assigned."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                            embed.timestamp = datetime.now()
                                        elif order_status == "AtRestaurant":
                                            embed.description = "**Driver has arrived at the Store** 🏪\n\nYour driver is waiting for the restaurant to prepare your order."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/RpwAYxrSG5sPWyqfn6ATu2TNjZmN6gkIkHtFFZZjTbM/https/i.gyazo.com/ee7b0b676c0801ccf07414cc4ab8e42a.gif?width=72&height=72")
                                            embed.timestamp = datetime.now()
                                        elif order_status == "FindingNewCourier":
                                            embed.description = "**Finding a new driver** 🚗\n\nThe previous driver is unavailable. A new driver will be assigned shortly."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/jvNBD8wrP7OzMoJqJaZ8e_V3bDyVvGy3djjZdtBynEc/https/i.gyazo.com/fae7779eb22aaf6a0284d927bfdea74b.gif?width=72&height=72")
                                            embed.timestamp = datetime.now()
                                        elif order_status == "OrderPlaced":
                                            embed.description = "**Order has been placed with the Store** 📝\n\nThe restaurant has received your order and will begin preparing it soon."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/I7dVXtanMLs7mQcko43gu-a7sk3Xs_uapvPvZubbAo8/https/i.gyazo.com/5ec4b7416f8329666b073f424a420949.png")
                                            embed.timestamp = datetime.now()
                                        elif order_status == "Preparing":
                                            embed.description = "**Store is preparing your order** 👨‍🍳\n\nThe restaurant is now cooking your food. It will be ready for pickup soon."
                                            embed.set_thumbnail(url="https://i.imgur.com/SAuvaKY.gif")
                                            embed.timestamp = datetime.now()
                                        elif order_status == "ReadyForPickup":
                                            embed.description = "**Order is ready for pickup** 📦\n\nYour food is ready and waiting for the driver to pick it up."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                            embed.timestamp = datetime.now()
                                        elif order_status == "PickedUp":
                                            embed.description = "**Driver has picked up your order** 🚗\n\nYour driver has your food and will be heading to your location soon."
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")
                                            embed.timestamp = datetime.now()
                                        elif order_status == "EnrouteToDropoff" or order_status == "EnrouteToEater":
                                            eta_text = None
                                            minutes_away = None
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/LkgLyEJqa5wnlm7K8SXj62nNOUoCFR0gkNbqmDIAv38/https/i.gyazo.com/52098aedb69916d2b79168c5b90725c6.gif?width=72&height=72")

                                            background_cards = order.get('backgroundFeedCards', [])
                                            logging.info("Checking for ETA updates...")

                                            for card in background_cards:
                                                if card.get('type') == 'mapEntity':
                                                    for entity in card.get('mapEntity', []):
                                                        if entity.get('type') == 'LABEL':
                                                            title = entity.get('title', '')
                                                            subtitle = entity.get('subtitle', [])

                                                            try:
                                                                minutes_away = int(title)
                                                                subtitle_text = ' '.join(subtitle) if subtitle else ''
                                                                eta_text = f"{minutes_away} {subtitle_text}"
                                                                embed.description = f"**Driver is on the way to you** 🚚\n\nYour driver is en route to your delivery location.\nEstimated arrival: **{eta_text}**"
                                                                embed.timestamp = datetime.now()
                                                            except (ValueError, TypeError) as e:
                                                                logging.error(f"Failed to parse minutes: {e}")
                                                            break

                                            if not eta_text:
                                                embed.description = "**Driver is on the way to you** 🚚\n\nYour driver is en route to your delivery location."
                                                embed.timestamp = datetime.now()

                                        elif order_status in ["TimeToMeetCourier", "ArrivedAtDropoff"]:
                                            embed.title = "🚨 Delivery Alert!"
                                            embed.description = "**The driver is about to drop off your order!**\n\n🔔 **Be ready to pick it up** 🔔\n\nYour driver has arrived at your location or will be there momentarily."
                                            embed.color = discord.Color.gold()
                                            embed.set_thumbnail(url="https://images-ext-1.discordapp.net/external/1pG3uOqsTvi3SZB5bcTG4I32e9bFkiYk9MpfLv45oBg/https/i.gyazo.com/a92427656e0c609ad54bb3c064ec099f.gif?width=72&height=72")
                                            embed.timestamp = datetime.now()

                                            # Only send role ping once per order
                                            if tracking_data and not tracking_data.get('delivery_ping_sent', False):
                                                await channel.send(f"<@&1340415428698308749>")
                                                tracking_data['delivery_ping_sent'] = True
                                                if save_tracking_func:
                                                    asyncio.create_task(save_tracking_func())
                                        else:
                                            embed.description = f"**Current Status:** {order_status}\n\nYour order is being processed. Please wait for updates."
                                            embed.timestamp = datetime.now()

                                        # We've removed the Order Status field for both bots

                                        # Extract store name from multiple possible locations
                                        store_name = 'Unknown Store'

                                        # Try to get from store object
                                        store_info = order.get('store', {})
                                        if store_info and store_info.get('title'):
                                            store_name = store_info.get('title')

                                        # Try to get from orderSummary
                                        order_summary = order.get('orderSummary', {})
                                        if order_summary and order_summary.get('restaurantName'):
                                            store_name = order_summary.get('restaurantName')

                                        # Add store information only if it's not 'Unknown Store'
                                        if store_name != 'Unknown Store' and not ('item' in store_name.lower() or '$' in store_name):
                                            embed.add_field(
                                                name="<:store:1360153496376315984> Restaurant",
                                                value=f"{store_name}",
                                                inline=False
                                            )

                                        # We don't add the Order Status or Track Order fields for either bot

                                        # Handle persistent embed logic
                                        if tracking_data and tracking_data.get('status_embed_message_id'):
                                            # Edit existing embed
                                            try:
                                                message = await channel.fetch_message(tracking_data['status_embed_message_id'])

                                                # Add queue position if channel is in queue
                                                try:
                                                    # Check if channel is in queue category (ID: 1389060752832200745)
                                                    QUEUE_CATEGORY_ID = 1389060752832200745
                                                    if hasattr(channel, 'category_id') and channel.category_id == QUEUE_CATEGORY_ID:
                                                        # Get queue category
                                                        queue_category = channel.guild.get_channel(QUEUE_CATEGORY_ID)
                                                        if queue_category:
                                                            # Get all channels in queue, sorted by position
                                                            queue_channels = sorted(queue_category.channels, key=lambda c: c.position)
                                                            # Find position of current channel (1-based)
                                                            queue_position = None
                                                            for i, queue_channel in enumerate(queue_channels):
                                                                if queue_channel.id == channel.id:
                                                                    queue_position = i + 1
                                                                    break

                                                            if queue_position:
                                                                embed.add_field(
                                                                    name="📊 Queue Position",
                                                                    value=f"#{queue_position} of {len(queue_channels)}",
                                                                    inline=True
                                                                )
                                                except Exception as e:
                                                    logging.error(f"Error getting queue position: {e}")

                                                # Update embed with status history
                                                status_history_text = format_status_history(tracking_data.get('status_history', []))
                                                embed.add_field(
                                                    name="📋 Status History",
                                                    value=status_history_text,
                                                    inline=False
                                                )

                                                # Create view with Status History button
                                                view = OrderStatusButtons(order_id, active_tracking_dict)
                                                await message.edit(embed=embed, view=view)
                                            except discord.NotFound:
                                                # Message was deleted, send new one and update ID

                                                # Add queue position if channel is in queue
                                                try:
                                                    # Check if channel is in queue category (ID: 1389060752832200745)
                                                    QUEUE_CATEGORY_ID = 1389060752832200745
                                                    if hasattr(channel, 'category_id') and channel.category_id == QUEUE_CATEGORY_ID:
                                                        # Get queue category
                                                        queue_category = channel.guild.get_channel(QUEUE_CATEGORY_ID)
                                                        if queue_category:
                                                            # Get all channels in queue, sorted by position
                                                            queue_channels = sorted(queue_category.channels, key=lambda c: c.position)
                                                            # Find position of current channel (1-based)
                                                            queue_position = None
                                                            for i, queue_channel in enumerate(queue_channels):
                                                                if queue_channel.id == channel.id:
                                                                    queue_position = i + 1
                                                                    break

                                                            if queue_position:
                                                                embed.add_field(
                                                                    name="📊 Queue Position",
                                                                    value=f"#{queue_position} of {len(queue_channels)}",
                                                                    inline=True
                                                                )
                                                except Exception as e:
                                                    logging.error(f"Error getting queue position: {e}")

                                                status_history_text = format_status_history(tracking_data.get('status_history', []))
                                                embed.add_field(
                                                    name="📋 Status History",
                                                    value=status_history_text,
                                                    inline=False
                                                )

                                                # Create view with Status History button
                                                view = OrderStatusButtons(order_id, active_tracking_dict)
                                                new_message = await channel.send(embed=embed, view=view)
                                                tracking_data['status_embed_message_id'] = new_message.id
                                                if save_tracking_func:
                                                    asyncio.create_task(save_tracking_func())
                                            except Exception as e:
                                                logging.error(f"Error editing persistent embed: {str(e)}")
                                                # Fallback to sending new message
                                                view = OrderStatusButtons(order_id, active_tracking_dict)
                                                await channel.send(embed=embed, view=view)
                                        else:
                                            # Send new embed and store message ID

                                            # Add queue position if channel is in queue
                                            try:
                                                # Check if channel is in queue category (ID: 1389060752832200745)
                                                QUEUE_CATEGORY_ID = 1389060752832200745
                                                if hasattr(channel, 'category_id') and channel.category_id == QUEUE_CATEGORY_ID:
                                                    # Get queue category
                                                    queue_category = channel.guild.get_channel(QUEUE_CATEGORY_ID)
                                                    if queue_category:
                                                        # Get all channels in queue, sorted by position
                                                        queue_channels = sorted(queue_category.channels, key=lambda c: c.position)
                                                        # Find position of current channel (1-based)
                                                        queue_position = None
                                                        for i, queue_channel in enumerate(queue_channels):
                                                            if queue_channel.id == channel.id:
                                                                queue_position = i + 1
                                                                break

                                                        if queue_position:
                                                            embed.add_field(
                                                                name="📊 Queue Position",
                                                                value=f"#{queue_position} of {len(queue_channels)}",
                                                                inline=True
                                                            )
                                            except Exception as e:
                                                logging.error(f"Error getting queue position: {e}")

                                            if tracking_data:
                                                status_history_text = format_status_history(tracking_data.get('status_history', []))
                                                embed.add_field(
                                                    name="📋 Status History",
                                                    value=status_history_text,
                                                    inline=False
                                                )

                                            # Create view with Status History button
                                            view = OrderStatusButtons(order_id, active_tracking_dict)
                                            new_message = await channel.send(embed=embed, view=view)

                                            if tracking_data:
                                                tracking_data['status_embed_message_id'] = new_message.id
                                                if save_tracking_func:
                                                    asyncio.create_task(save_tracking_func())
                                    break
                                elif response.status == 429:
                                    # Rate limited, wait longer
                                    await asyncio.sleep(5 * (retry + 1))
                                else:
                                    await asyncio.sleep(2 * (retry + 1))
                        except asyncio.TimeoutError:
                            if retry == 2:  # Last retry
                                raise
                            await asyncio.sleep(2 * (retry + 1))
                            continue

                    # Add delay between checks
                    await asyncio.sleep(30)  # Check every 30 seconds

                except Exception as e:
                    logging.error(f"Error during order tracking: {str(e)}")
                    await asyncio.sleep(5)  # Wait before retrying
                    continue

                # No need for another delay here
        finally:
            # Close the session if we created it
            if should_close_session and session and not session.closed:
                await session.close()
    except Exception as e:
        logging.error(f"❌ Error tracking order {order_id}: {str(e)}")
        try:
            await channel.send(f"❌ Error tracking order: {str(e)}")
        except discord.errors.Forbidden:
            logging.error(f"Cannot send messages in channel {channel.id}")

async def vouchtop(interaction: discord.Interaction):
    """Display the vouch leaderboard."""
    try:
        # Change defer to be ephemeral
        await interaction.response.defer(ephemeral=True)
        logging.info(f"📊 `/vouchtop` triggered by {interaction.user}")

        # Use the vouch channel ID
        VOUCH_CHANNEL_ID = 1340392172691918848

        sorted_stats, total_images = await count_vouches(VOUCH_CHANNEL_ID)

        if sorted_stats is None:
            await interaction.followup.send("❌ Failed to fetch vouch statistics.", ephemeral=True)
            return

        paginator = VouchPaginator(sorted_stats, total_images)
        # followup.send is already ephemeral because we deferred with ephemeral=True
        await interaction.followup.send(embed=paginator.get_embed(), view=paginator)

        logging.info("✅ Vouch statistics displayed successfully")

    except Exception as e:
        logging.error(f"❌ Error in vouchtop command: {str(e)}")
        await interaction.followup.send(
            "❌ An error occurred while processing the command.",
            ephemeral=True
        )

async def selectedstores(interaction: discord.Interaction, address: str):
    """Gets a promo link for the specified address and saves store list."""
    await interaction.response.defer()

    try:
        # First get the coordinates and reference from HERE API
        HERE_API_KEY = "bC_6WY1VB6RlWhocMPiFlrmRzFY8L_LNah8iij8ietI"
        geocode_url = f"https://geocode.search.hereapi.com/v1/geocode?q={urllib.parse.quote(address)}&apiKey={HERE_API_KEY}"

        async with aiohttp.ClientSession() as session:
            # Get coordinates
            async with session.get(geocode_url) as response:
                location_data = await response.json()

                if not location_data.get('items'):
                    raise ValueError("Address not found")

                location = location_data['items'][0]
                position = location['position']

                # Create the location payload for the promo link
                location_payload = {
                    "address": address,
                    "reference": location.get('id', ''),
                    "referenceType": "here_places",
                    "latitude": position['lat'],
                    "longitude": position['lng']
                }

                # Encode payload for promo link
                json_payload = json.dumps(location_payload)
                base64_payload = base64.b64encode(json_payload.encode()).decode()
                encoded_payload = urllib.parse.quote(base64_payload)

                # Create the Uber Eats promo link
                promo_link = (
                    "https://www.ubereats.com/marketing"
                    "?mft=TARGETING_STORE_PROMO"
                    f"&pl={encoded_payload}"
                    "&promotionUuid=449baf92-dda2-44d1-9ad6-24033c26f516"
                    "&targetingStoreTag=restaurant_us_target_all"
                )

                # Now fetch store list using getSearchHomeV2 API
                search_url = "https://www.ubereats.com/_p/api/getSearchHomeV2"
                headers = {
                    "accept": "*/*",
                    "content-type": "application/json",
                    "x-csrf-token": "x",
                    "cookie": UBER_COOKIE,
                }

                search_payload = {
                    "userQuery": "",
                    "latitude": position['lat'],
                    "longitude": position['lng'],
                    "address": address,
                    "reference": location.get('id', ''),
                    "referenceType": "here_places",
                    "limit": 100,
                    "feedSessionCount": {"announcementCount": 0, "announcementLabel": ""}
                }

                async with session.post(search_url, headers=headers, json=search_payload) as search_response:
                    stores_data = await search_response.json()

                    # Extract store information
                    stores = []
                    if 'data' in stores_data and 'feedItems' in stores_data['data']:
                        for item in stores_data['data']['feedItems']:
                            if 'store' in item:
                                store = item['store']
                                store_info = {
                                    'name': store.get('title', 'Unknown'),
                                    'rating': store.get('rating', {}).get('ratingValue', 'N/A'),
                                    'reviews': store.get('rating', {}).get('reviewCount', 'N/A'),
                                    'status': store.get('status', {}).get('text', 'Unknown'),
                                    'categories': [cat.get('name') for cat in store.get('categories', [])]
                                }
                                stores.append(store_info)

                    # Save stores to file
                    with open('selectedstores.txt', 'w', encoding='utf-8') as f:
                        f.write(f"Stores available at: {address}\n")
                        f.write("=" * 50 + "\n\n")
                        for store in stores:
                            f.write(f"Name: {store['name']}\n")
                            f.write(f"Rating: {store['rating']} ({store['reviews']} reviews)\n")
                            f.write(f"Status: {store['status']}\n")
                            f.write(f"Categories: {', '.join(store['categories'])}\n")
                            f.write("-" * 30 + "\n")

                    # Create embed with promo link only
                    embed = discord.Embed(
                        title="🎉 Uber Eats Promo Link",
                        description=f"**Delivery Address:** {address}\n\n"
                                  f"🔗 [Click here to open Uber Eats with this address]({promo_link})",
                        color=discord.Color.blue()
                    )

                await interaction.followup.send(embed=embed)
                logging.info(f"✅ Generated promo link for address: {address}")

    except Exception as e:
        logging.error(f"Error in selectedstores command: {e}")
        await interaction.followup.send(
            "❌ An error occurred while generating the promo link. Please try again later.",
            ephemeral=True
        )

def process_summary_text(summary_text, fixed_discount_amount=20.00):
    """Processes the order summary text and generates the embed."""
    if not summary_text:
        return None, 0, 0

    # Extract values from summary text
    subtotal_match = re.search(r"Subtotal:\s*\$?(\d+\.?\d*)", summary_text)
    delivery_match = re.search(r"Delivery Fee:\s*\$?(\d+\.?\d*)", summary_text)
    taxes_match = re.search(r"Taxes:\s*\$?(\d+\.?\d*)", summary_text)
    is_cad = 'CA$' in summary_text

    # Convert extracted values to float
    subtotal = float(subtotal_match.group(1).replace(',', '')) if subtotal_match else 0.0
    delivery_fee = float(delivery_match.group(1).replace(',', '')) if delivery_match else 0.0
    taxes = float(taxes_match.group(1).replace(',', '')) if taxes_match else 0.0

    # Always calculate as if promo is available
    discount_amount = 26.00 if is_cad else fixed_discount_amount  # CAD gets $26 discount

    # Calculate overflow fee based on correct thresholds
    threshold = 30 if is_cad else 25  # $25 for US orders, $30 for Canadian orders
    overflow_fee = round(max(0, subtotal - threshold), 2)

    # Calculate final totals including overflow fee
    discounted_subtotal = max(0, subtotal - discount_amount)
    final_total = round(discounted_subtotal + overflow_fee + taxes + delivery_fee, 2)
    total_before_discount = round(subtotal + delivery_fee + taxes + overflow_fee, 2)
    savings = round(total_before_discount - final_total, 2)

    # Create embed
    embed = discord.Embed(
        title="💰 Order Price Breakdown",
        color=discord.Color.green()
    )

    currency = "CA$" if is_cad else "$"

    embed.add_field(
        name="Original Subtotal",
        value=f"{currency}{subtotal:.2f}",
        inline=True
    )

    embed.add_field(
        name="Discount Amount",
        value=f"-{currency}{discount_amount:.2f}",
        inline=True
    )

    if overflow_fee > 0:
        embed.add_field(
            name="Overflow Fee",
            value=f"{currency}{overflow_fee:.2f}",
            inline=True
        )

    embed.add_field(
        name="Delivery Fee",
        value=f"{currency}{delivery_fee:.2f}",
        inline=True
    )

    embed.add_field(
        name="Taxes",
        value=f"{currency}{taxes:.2f}",
        inline=True
    )

    embed.add_field(
        name="Final Total",
        value=f"{currency}{final_total:.2f}",
        inline=True
    )

    embed.add_field(
        name="You Save",
        value=f"{currency}{savings:.2f}",
        inline=True
    )

    return embed, subtotal, final_total

def format_cart_items(cart_items):
    """Format cart items for display in embeds."""
    formatted_items = []
    for item in cart_items:
        name = item.get('name', 'Unknown Item')
        quantity = item.get('quantity', 1)
        price = item.get('price', 0.0)

        if quantity > 1:
            formatted_items.append(f"{name} (x{quantity}) - ${price:.2f}")
        else:
            formatted_items.append(f"{name} - ${price:.2f}")

    return formatted_items

async def latestsummary(
    interaction: discord.Interaction,
    subtotal: float,
    fees: float,
    grouporderlink: str = None
):
    """Calculate and display an order summary with manual subtotal and fees."""
    try:
        logging.info(f"🚀 LATESTSUMMARY COMMAND START")
        logging.info(f"👤 Triggered by: {interaction.user} (ID: {interaction.user.id})")
        logging.info(f"💲 Input - Subtotal: ${subtotal:.2f}, Fees: ${fees:.2f}")
        logging.info(f"🔗 Group Order Link: {grouporderlink}")

        await interaction.response.defer()
        logging.info("⏳ Interaction deferred")

        # Initialize variables
        location = "Location not provided"
        cart_items = []

        if grouporderlink:
            logging.info(f"🔄 Processing group order link...")
            result = await process_group_order(grouporderlink)

            if result:
                # Ensure the group link is included in the result
                result['group_link'] = grouporderlink
                logging.info("✅ Group order processed successfully")

                loc = result.get('location', {})
                if loc:
                    location = f"{loc.get('address', 'Unknown')}, {loc.get('city', 'Unknown')}, {loc.get('state', 'XX').upper()}"
                    logging.info(f"📍 Location extracted: {location}")

                # Format cart items from dictionaries to strings
                raw_cart_items = result.get('cart_items', [])
                if raw_cart_items:
                    cart_items = format_cart_items(raw_cart_items)
                    logging.info(f"🛒 Retrieved {len(cart_items)} cart items")
                else:
                    logging.warning("⚠️ No cart items in result")

        # If no group order link provided, search in chat history
        if not grouporderlink:
            logging.info("🔍 No direct group order link - Searching channel history...")
            message_count = 0
            found_link = False

            async for message in interaction.channel.history(limit=50):
                message_count += 1
                logging.info(f"📝 Checking message {message_count}")

                pattern = r"https://(?:eats\.uber\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/|www\.ubereats\.com/(?:[a-z]{2}(?:-[a-z]{2})?/)?group-orders/)[a-zA-Z0-9-]+(?:/join)?(?:\S*)"

                match = re.search(pattern, message.content)
                if match:
                    grouporderlink = match.group(0)
                    logging.info(f"✅ Found group order link: {grouporderlink}")
                    found_link = True
                    break

                for embed in message.embeds:
                    if embed.description:
                        match = re.search(pattern, embed.description)
                        if match:
                            grouporderlink = match.group(0)
                            logging.info(f"✅ Found group order link in embed: {grouporderlink}")
                            found_link = True
                            break

                if found_link:
                    break

            # Process group order if we found a link
            if grouporderlink:
                logging.info(f"🔄 Processing found group order link...")
                result = await process_group_order(grouporderlink)

                if result:
                    logging.info("✅ Group order processed successfully")

                    loc = result.get('location', {})
                    if loc:
                        location = f"{loc.get('address', 'Unknown')}, {loc.get('city', 'Unknown')}, {loc.get('state', 'XX').upper()}"
                        logging.info(f"📍 Location extracted: {location}")

                    # Format cart items from dictionaries to strings
                    raw_cart_items = result.get('cart_items', [])
                    if raw_cart_items:
                        cart_items = format_cart_items(raw_cart_items)
                        logging.info(f"🛒 Retrieved {len(cart_items)} cart items")
                    else:
                        logging.warning("⚠️ No cart items in result")

        # For latestsummary, we'll use the exact fees input by the user
        # without calculating overflow fees

        # Set fixed discount amount ($20 for USD)
        fixed_discount_amount = 20.00

        # Calculate discounted subtotal and savings
        discounted_subtotal = max(0, subtotal - fixed_discount_amount)
        savings = round(subtotal - discounted_subtotal, 2)

        # Create fee calculations dictionary with user-provided fees
        # and NO overflow fee, but including method fee
        method_fee = 7.50  # Method fee component

        fee_calculations = {
            'subtotal': subtotal,
            'discounted_subtotal': discounted_subtotal,
            'savings': savings,
            'overflow_fee': 0,  # No overflow fee for latestsummary
            'service_fee': (fees - method_fee) * 0.5,  # Estimate service fee as 50% of remaining fees
            'taxes': (fees - method_fee) * 0.5,        # Estimate taxes as 50% of remaining fees
            'ca_driver_benefit': 0,
            'uber_one_discount': 0,
            'method_fee': method_fee,     # Include method fee
            'fixed_fee': method_fee,      # Backward compatibility
            'total_fees': fees,         # Use the exact fees input by the user
            'final_fees': fees,         # Use the exact fees input by the user
            'final_total': round(discounted_subtotal + fees, 2),  # No overflow fee
            'is_cad': False
        }

        # Create result dictionary for the embed
        result = {
            'group_link': grouporderlink if grouporderlink else "Not available",
            'location': {'address': location},
            'is_promo': True,  # Assume it's in promo
            'store_url': ""    # No store URL available
        }

        # Import the embed template
        try:
            from themethodbot.embed_templates import create_latestsummary_embed
            # Create and send the embed
            embed = create_latestsummary_embed(result, cart_items, fee_calculations)
        except ImportError:
            # Fallback to basic embed if template not available
            embed = discord.Embed(
                title="📊 Latest Summary",
                description=f"**Location:** {location}",
                color=discord.Color.green()
            )
            embed.add_field(name="Original Subtotal", value=f"${subtotal:.2f}", inline=True)
            embed.add_field(name="Discounted Subtotal", value=f"${discounted_subtotal:.2f}", inline=True)
            embed.add_field(name="Fees & Taxes", value=f"${fees:.2f}", inline=True)
            embed.add_field(name="Final Total", value=f"${fee_calculations['final_total']:.2f}", inline=True)
            embed.add_field(name="You Save", value=f"${savings:.2f}", inline=True)

        # Try to import payment methods selector, fallback if not available
        try:
            from themethodbot.paymentapp import PaymentMethodSelector
            view = PaymentMethodSelector()
        except ImportError:
            view = None

        # Send the embed with payment methods buttons if available
        if view:
            await interaction.followup.send(embed=embed, view=view)
        else:
            await interaction.followup.send(embed=embed)

        logging.info("✅ Summary sent successfully")

    except Exception as e:
        logging.error(f"❌ Error in latestsummary: {str(e)}")
        logging.error(traceback.format_exc())
        try:
            await interaction.followup.send(
                "❌ An error occurred while processing the summary.",
                ephemeral=True
            )
        except:
            pass
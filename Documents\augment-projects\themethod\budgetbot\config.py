"""
BudgetBot Configuration
Budget-specific settings, constants, and configurations for BudgetBot.
"""

import os
from typing import Dict, Any

# BudgetBot Branding
BUDGETBOT_NAME = "BudgetBot"
BUDGETBOT_VERSION = "1.0.0"
BUDGETBOT_DESCRIPTION = "Budget-friendly Discord bot for The Method service"

# BudgetBot Colors (RGB values)
BUDGETBOT_COLORS = {
    'primary': (255, 165, 0),      # Orange - budget-friendly
    'secondary': (255, 140, 0),    # Darker orange
    'success': (0, 255, 0),        # Green
    'error': (255, 0, 0),          # Red
    'warning': (255, 255, 0),      # Yellow
    'info': (0, 191, 255)          # Light blue
}

# BudgetBot Pricing Configuration
BUDGETBOT_PRICING = {
    'min_subtotal_usd': 20.0,      # Same as themethodbot after update
    'max_subtotal_usd': 35.0,      # Same as themethodbot after update
    'min_subtotal_cad': 30.0,      # Same as themethodbot
    'max_subtotal_cad': 35.0,      # Same as themethodbot
    'fixed_discount_usd': 25.00,   # Same as themethodbot after update
    'fixed_discount_cad': 32.50,   # Same as themethodbot after update
    'method_fee_usd': 10.00,       # Same as themethodbot after update
    'method_fee_cad': 13.00,       # Same as themethodbot after update
    'overflow_threshold_usd': 20.0, # Same as themethodbot after update
    'overflow_threshold_cad': 30.0  # Same as themethodbot
}

# BudgetBot Channel Configuration (to be customized per server)
BUDGETBOT_CHANNELS = {
    'queue_category_id': 1389060752832200745,    # Same as themethodbot for now
    'delivering_category_id': 1389060752832200745, # Same as themethodbot for now
    'status_channel_id': None,                    # To be configured
    'log_channel_id': None                        # To be configured
}

# BudgetBot Messages
BUDGETBOT_MESSAGES = {
    'store_open': "🍔 **BudgetBot Store is Now Open!** 💰\nWe're accepting budget-friendly orders!",
    'store_closed': "🔒 **BudgetBot Store is Now Closed** 💤\nWe're not accepting orders at the moment.",
    'order_completion': "Enjoy your budget-friendly order! 🍔💰 Please consider leaving a review!",
    'welcome': "Welcome to BudgetBot - your budget-friendly food ordering assistant! 💰",
    'help': "BudgetBot helps you place budget-friendly food orders with simplified pricing!"
}

# BudgetBot Emojis
BUDGETBOT_EMOJIS = {
    'burger': '🍔',
    'money': '💰',
    'cart': '🛒',
    'location': '📍',
    'restaurant': '🏪',
    'check': '✅',
    'error': '❌',
    'warning': '⚠️',
    'loading': '⏳',
    'track': '🔍',
    'open': '🔓',
    'closed': '🔒',
    'budget': '💸',
    'link': '🔗',
    'success': '🎉'
}

# Order Link Detection Patterns
BUDGETBOT_LINK_PATTERNS = [
    r'https?://(?:www\.)?ubereats\.com/.*',
    r'https?://(?:www\.)?uber\.com/.*food.*',
    r'https?://.*ubereats\.com.*',
    r'(?:www\.)?ubereats\.com/.*'
]

# BudgetBot File Paths
BUDGETBOT_FILES = {
    'tracking_data': 'budgetbot_tracking_data.json',
    'status_embed_data': 'budgetbot_status_embed_data.json',
    'log_file': 'budgetbot.log',
    'config_file': 'budgetbot_config.json'
}

# BudgetBot Environment Variables
BUDGETBOT_ENV_VARS = {
    'token': 'BUDGET_DISCORD_BOT_TOKEN',
    'guild_id': 'BUDGET_DISCORD_GUILD_ID',
    'commands_global': 'COMMANDS_GLOBAL',
    'uber_cookie': 'UBER_COOKIE',
    'canada_cookie': 'CANADA_COOKIE'
}

# BudgetBot Command Configuration
BUDGETBOT_COMMANDS = {
    'ordersuccess': {
        'name': 'ordersuccess',
        'description': 'Mark an order as successfully completed',
        'enabled': True
    },
    'track': {
        'name': 'track',
        'description': 'Track an order status with budget-friendly updates',
        'enabled': True
    }
}

# BudgetBot Feature Flags
BUDGETBOT_FEATURES = {
    'simplified_embeds': True,          # Use simplified embed format
    'persistent_embeds': True,          # Use persistent embed system
    'auto_delivered_cleanup': True,     # Automatic delivered order cleanup
    'order_tracking': True,             # Order tracking functionality
    'auto_link_detection': True,        # Automatic order link detection
    'order_success_tracking': True,     # Order success marking functionality
    'budget_messaging': True,           # Budget-specific messaging
    'metrics_tracking': True            # Command metrics tracking
}

def get_budgetbot_config() -> Dict[str, Any]:
    """Get complete BudgetBot configuration."""
    return {
        'name': BUDGETBOT_NAME,
        'version': BUDGETBOT_VERSION,
        'description': BUDGETBOT_DESCRIPTION,
        'colors': BUDGETBOT_COLORS,
        'pricing': BUDGETBOT_PRICING,
        'channels': BUDGETBOT_CHANNELS,
        'messages': BUDGETBOT_MESSAGES,
        'emojis': BUDGETBOT_EMOJIS,
        'files': BUDGETBOT_FILES,
        'env_vars': BUDGETBOT_ENV_VARS,
        'commands': BUDGETBOT_COMMANDS,
        'features': BUDGETBOT_FEATURES
    }

def validate_budgetbot_config() -> bool:
    """Validate BudgetBot configuration."""
    try:
        # Check required environment variables
        required_env_vars = ['BUDGET_DISCORD_BOT_TOKEN', 'BUDGET_DISCORD_GUILD_ID']
        for var in required_env_vars:
            if not os.getenv(var):
                print(f"❌ Missing required environment variable: {var}")
                return False
        
        # Validate pricing configuration
        pricing = BUDGETBOT_PRICING
        if pricing['min_subtotal_usd'] >= pricing['max_subtotal_usd']:
            print("❌ Invalid USD subtotal range")
            return False
        
        if pricing['min_subtotal_cad'] >= pricing['max_subtotal_cad']:
            print("❌ Invalid CAD subtotal range")
            return False
        
        print("✅ BudgetBot configuration is valid")
        return True
        
    except Exception as e:
        print(f"❌ Error validating BudgetBot configuration: {e}")
        return False

if __name__ == "__main__":
    # Test configuration
    config = get_budgetbot_config()
    print("BudgetBot Configuration:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    
    print("\nValidating configuration...")
    validate_budgetbot_config()

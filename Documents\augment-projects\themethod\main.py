import logging
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('main.log', encoding='utf-8')
    ]
)

logger = logging.getLogger('main')

def run_bot(bot_name):
    """Run a specific bot by name."""
    try:
        if bot_name == "themethodbot":
            from themethodbot import themethodbot
            themethodbot.run_bot()
        else:
            logger.error(f"Unknown bot: {bot_name}")
            print(f"Unknown bot: {bot_name}")
            print("Available bots: themethodbot")
    except Exception as e:
        logger.error(f"Error running {bot_name}: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Run The Method Discord bot')
    parser.add_argument('bot', choices=['themethodbot'],
                        help='The bot to run (only themethodbot is available)')

    args = parser.parse_args()

    run_bot(args.bot)

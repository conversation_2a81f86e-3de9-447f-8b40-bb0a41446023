#!/usr/bin/env python3
"""
Test script for persistent embed system in themethodbot.

This script tests the new persistent embed functionality for order status tracking,
including:
1. Status history management functions
2. Discord timestamp formatting
3. Persistent embed data structure
4. Role ping logic (once per order)

Run this script to verify the implementation works correctly.
"""

import sys
import os
import time
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the functions we want to test
from common.bot import (
    add_status_to_history,
    format_status_history,
    get_status_display_text
)

def test_status_display_text():
    """Test the status display text conversion function."""
    print("🧪 Testing status display text conversion...")
    
    test_cases = [
        ("EnrouteToRestaurant", "Driver is heading to the store"),
        ("AtRestaurant", "Driver has arrived at the store"),
        ("EnrouteToDropoff", "Driver is on the way to you"),
        ("EnrouteToEater", "Driver is on the way to you"),
        ("TimeToMeetCourier", "The driver is about to drop off your order!"),
        ("ArrivedAtDropoff", "The driver is about to drop off your order!"),
        ("DELIVERED", "Order delivered!"),
        ("CANCELED", "Order canceled"),
        ("UnknownStatus", "Status: UnknownStatus")
    ]
    
    for status_code, expected in test_cases:
        result = get_status_display_text(status_code)
        if result == expected:
            print(f"  ✅ {status_code} -> {result}")
        else:
            print(f"  ❌ {status_code} -> Expected: {expected}, Got: {result}")
    
    print()

def test_status_history_management():
    """Test the status history management functions."""
    print("🧪 Testing status history management...")
    
    # Test adding status to empty history
    history = []
    history = add_status_to_history(history, "Driver is heading to the store")
    
    if len(history) == 1:
        print("  ✅ Added first status to empty history")
    else:
        print(f"  ❌ Expected 1 status, got {len(history)}")
    
    # Test adding different status
    history = add_status_to_history(history, "Driver has arrived at the store")
    
    if len(history) == 2:
        print("  ✅ Added second different status")
    else:
        print(f"  ❌ Expected 2 statuses, got {len(history)}")
    
    # Test adding duplicate status (should not add)
    history = add_status_to_history(history, "Driver has arrived at the store")
    
    if len(history) == 2:
        print("  ✅ Duplicate status not added")
    else:
        print(f"  ❌ Expected 2 statuses (no duplicate), got {len(history)}")
    
    # Test timestamp functionality
    current_time = int(time.time())
    history = add_status_to_history(history, "Driver is on the way to you", current_time)
    
    if len(history) == 3 and history[-1]['timestamp'] == current_time:
        print("  ✅ Custom timestamp added correctly")
    else:
        print(f"  ❌ Custom timestamp failed")
    
    # Test Discord timestamp formatting
    last_entry = history[-1]
    expected_discord_timestamp = f"<t:{current_time}:t>"
    
    if last_entry['discord_timestamp'] == expected_discord_timestamp:
        print("  ✅ Discord timestamp formatted correctly")
    else:
        print(f"  ❌ Discord timestamp format incorrect: {last_entry['discord_timestamp']}")
    
    print()
    return history

def test_status_history_formatting():
    """Test the status history formatting for embed display."""
    print("🧪 Testing status history formatting...")
    
    # Test empty history
    empty_formatted = format_status_history([])
    if empty_formatted == "No status updates yet.":
        print("  ✅ Empty history formatted correctly")
    else:
        print(f"  ❌ Empty history format incorrect: {empty_formatted}")
    
    # Test with sample history
    sample_history = [
        {
            'status': 'Driver is heading to the store',
            'timestamp': 1640995200,  # Example timestamp
            'discord_timestamp': '<t:1640995200:t>'
        },
        {
            'status': 'Driver has arrived at the store',
            'timestamp': 1640995800,
            'discord_timestamp': '<t:1640995800:t>'
        },
        {
            'status': 'Driver is on the way to you',
            'timestamp': 1640996400,
            'discord_timestamp': '<t:1640996400:t>'
        }
    ]
    
    formatted = format_status_history(sample_history)
    expected_lines = [
        "<t:1640995200:t> Driver is heading to the store",
        "<t:1640995800:t> Driver has arrived at the store",
        "<t:1640996400:t> Driver is on the way to you"
    ]
    expected_formatted = "\n".join(expected_lines)
    
    if formatted == expected_formatted:
        print("  ✅ Status history formatted correctly")
        print(f"    Sample output:\n{formatted}")
    else:
        print(f"  ❌ Status history format incorrect")
        print(f"    Expected:\n{expected_formatted}")
        print(f"    Got:\n{formatted}")
    
    print()

def test_tracking_data_structure():
    """Test the tracking data structure format."""
    print("🧪 Testing tracking data structure...")
    
    # Simulate the expected tracking data structure
    sample_tracking_data = {
        'channel_id': 123456789,
        'start_time': time.time(),
        'last_status': 'EnrouteToDropoff',
        'order_link': 'https://www.ubereats.com/orders/test-order-id',
        'status_embed_message_id': 987654321,
        'status_history': [
            {
                'status': 'Driver is heading to the store',
                'timestamp': int(time.time()) - 600,
                'discord_timestamp': f'<t:{int(time.time()) - 600}:t>'
            },
            {
                'status': 'Driver is on the way to you',
                'timestamp': int(time.time()),
                'discord_timestamp': f'<t:{int(time.time())}:t>'
            }
        ],
        'delivery_ping_sent': False
    }
    
    # Check required fields
    required_fields = [
        'channel_id', 'start_time', 'last_status', 'order_link',
        'status_embed_message_id', 'status_history', 'delivery_ping_sent'
    ]
    
    missing_fields = []
    for field in required_fields:
        if field not in sample_tracking_data:
            missing_fields.append(field)
    
    if not missing_fields:
        print("  ✅ All required fields present in tracking data structure")
    else:
        print(f"  ❌ Missing fields: {missing_fields}")
    
    # Check data types
    type_checks = [
        ('channel_id', int),
        ('start_time', (int, float)),
        ('last_status', str),
        ('order_link', str),
        ('status_embed_message_id', (int, type(None))),
        ('status_history', list),
        ('delivery_ping_sent', bool)
    ]
    
    for field, expected_type in type_checks:
        value = sample_tracking_data[field]
        if isinstance(value, expected_type):
            print(f"  ✅ {field} has correct type: {type(value).__name__}")
        else:
            print(f"  ❌ {field} has incorrect type: expected {expected_type}, got {type(value)}")
    
    print()

def main():
    """Run all tests."""
    print("🚀 Starting persistent embed system tests...\n")
    
    try:
        test_status_display_text()
        history = test_status_history_management()
        test_status_history_formatting()
        test_tracking_data_structure()
        
        print("✅ All tests completed!")
        print("\n📋 Summary:")
        print("- Status display text conversion: Working")
        print("- Status history management: Working")
        print("- Discord timestamp formatting: Working")
        print("- Tracking data structure: Working")
        print("\n🎯 The persistent embed system is ready for deployment!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())

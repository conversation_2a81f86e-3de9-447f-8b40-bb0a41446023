#!/usr/bin/env python3
"""
Debug script to investigate BudgetBot environment variable loading.
"""

import os
import sys
from dotenv import load_dotenv

print("🔍 BUDGETBOT ENVIRONMENT DEBUG")
print("=" * 50)

print(f"Current working directory: {os.getcwd()}")
print(f"Script location: {os.path.dirname(os.path.abspath(__file__))}")

# Check if .env files exist
env_files = [
    ".env",
    "themethodbot/.env",
    "budgetbot/.env"
]

print("\n📁 Checking for .env files:")
for env_file in env_files:
    if os.path.exists(env_file):
        print(f"   ✅ Found: {env_file}")
        try:
            with open(env_file, 'r') as f:
                content = f.read()
                if 'BUDGET_DISCORD_BOT_TOKEN' in content:
                    # Extract just the first 20 characters of the token for security
                    lines = content.split('\n')
                    for line in lines:
                        if line.startswith('BUDGET_DISCORD_BOT_TOKEN='):
                            token = line.split('=', 1)[1]
                            print(f"      BUDGET_DISCORD_BOT_TOKEN: {token[:20]}...")
                            break
                else:
                    print(f"      No BUDGET_DISCORD_BOT_TOKEN found")
        except Exception as e:
            print(f"      Error reading file: {e}")
    else:
        print(f"   ❌ Not found: {env_file}")

print("\n🔧 Testing dotenv loading:")

# Test loading without specifying path
print("1. Loading dotenv() without path:")
load_dotenv()
token1 = os.getenv('BUDGET_DISCORD_BOT_TOKEN')
print(f"   BUDGET_DISCORD_BOT_TOKEN: {token1[:20] + '...' if token1 else 'NOT FOUND'}")

# Clear environment and test loading with explicit path
if 'BUDGET_DISCORD_BOT_TOKEN' in os.environ:
    del os.environ['BUDGET_DISCORD_BOT_TOKEN']

print("2. Loading dotenv('.env') with explicit path:")
load_dotenv('.env')
token2 = os.getenv('BUDGET_DISCORD_BOT_TOKEN')
print(f"   BUDGET_DISCORD_BOT_TOKEN: {token2[:20] + '...' if token2 else 'NOT FOUND'}")

# Test what BudgetBot would actually load
print("\n🤖 Simulating BudgetBot loading:")
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Clear environment
if 'BUDGET_DISCORD_BOT_TOKEN' in os.environ:
    del os.environ['BUDGET_DISCORD_BOT_TOKEN']

# Simulate BudgetBot's loading process
from dotenv import load_dotenv
load_dotenv()  # This is what BudgetBot does

DISCORD_BOT_TOKEN = os.getenv('BUDGET_DISCORD_BOT_TOKEN')
print(f"BudgetBot would get token: {DISCORD_BOT_TOKEN[:20] + '...' if DISCORD_BOT_TOKEN else 'NOT FOUND'}")

# Check all environment variables that contain 'TOKEN'
print("\n🔍 All TOKEN environment variables:")
for key, value in os.environ.items():
    if 'TOKEN' in key.upper():
        print(f"   {key}: {value[:20] + '...' if value else 'EMPTY'}")

print("\n✅ Debug complete!")

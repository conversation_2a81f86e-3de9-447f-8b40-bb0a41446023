#!/usr/bin/env python3
"""
BudgetBot Token Update Helper
Safely updates the BUDGET_DISCORD_BOT_TOKEN in the .env file and validates it.
"""

import os
import re
import asyncio
from validate_discord_token import validate_discord_token

def update_env_token(new_token):
    """Update the BUDGET_DISCORD_BOT_TOKEN in the .env file."""
    env_file_path = '.env'
    
    if not os.path.exists(env_file_path):
        print(f"❌ .env file not found at {env_file_path}")
        return False
    
    try:
        # Read the current .env file
        with open(env_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create backup
        backup_path = '.env.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Created backup: {backup_path}")
        
        # Update the token
        pattern = r'^BUDGET_DISCORD_BOT_TOKEN=.*$'
        replacement = f'BUDGET_DISCORD_BOT_TOKEN={new_token}'
        
        if re.search(pattern, content, re.MULTILINE):
            # Replace existing token
            new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            print("✅ Found existing BUDGET_DISCORD_BOT_TOKEN, updating...")
        else:
            # Add new token
            new_content = content + f'\n{replacement}\n'
            print("✅ Adding new BUDGET_DISCORD_BOT_TOKEN...")
        
        # Write the updated content
        with open(env_file_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ Successfully updated {env_file_path}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating .env file: {e}")
        return False

async def main():
    """Main function to update and validate token."""
    print("🔧 BUDGETBOT TOKEN UPDATE HELPER")
    print("=" * 50)
    
    print("This script will help you update the BUDGET_DISCORD_BOT_TOKEN")
    print("with the correct token for the 'Quick Eats' bot.\n")
    
    # Get new token from user
    print("📋 INSTRUCTIONS:")
    print("1. Go to Discord Developer Portal: https://discord.com/developers/applications")
    print("2. Find the 'Quick Eats' bot application")
    print("3. Go to the 'Bot' section")
    print("4. Copy the bot token (click 'Reset Token' if needed)")
    print("5. Paste the token below\n")
    
    new_token = input("🔑 Enter the new Discord bot token for 'Quick Eats': ").strip()
    
    if not new_token:
        print("❌ No token provided. Exiting.")
        return
    
    # Basic validation
    if len(new_token) < 50 or '.' not in new_token:
        print("❌ Token format looks invalid. Discord tokens are usually 70+ characters with dots.")
        return
    
    print(f"\n🔍 Token preview: {new_token[:20]}...{new_token[-10:]}")
    
    # Confirm update
    confirm = input("\n❓ Update the .env file with this token? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ Update cancelled.")
        return
    
    # Update the .env file
    print("\n📝 Updating .env file...")
    if not update_env_token(new_token):
        return
    
    # Validate the new token
    print("\n🔍 Validating new token with Discord...")
    is_valid, bot_data = await validate_discord_token(new_token)
    
    if is_valid and bot_data:
        bot_name = f"{bot_data.get('username')}#{bot_data.get('discriminator')}"
        print(f"\n🎉 SUCCESS! Token is valid for: {bot_name}")
        
        if "Quick Eats" in bot_data.get('username', ''):
            print("✅ Perfect! This is the 'Quick Eats' bot.")
        else:
            print(f"⚠️  Warning: This bot is named '{bot_data.get('username')}', not 'Quick Eats'")
            print("   Make sure this is the correct bot.")
        
        print(f"\n🚀 Ready to test! Run: python main.py budgetbot")
        
    else:
        print("\n❌ Token validation failed. Please check the token and try again.")
        print("   The .env file has been updated, but the token may still be invalid.")

if __name__ == "__main__":
    asyncio.run(main())

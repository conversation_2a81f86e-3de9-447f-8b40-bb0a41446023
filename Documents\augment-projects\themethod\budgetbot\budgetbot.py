#!/usr/bin/env python3
"""
BudgetBot - A budget-focused Discord bot for The Method service
Integrated with themethodbot's latest improvements including:
- Simplified order summary embeds
- Persistent embed system
- Automatic delivered order cleanup
- Updated pricing model
"""

import discord
from discord.ext import commands
from discord import app_commands
import asyncio
import logging
import os
import sys
import time
import traceback
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv
import json
from datetime import datetime

# Add parent directory to path for common imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import common functionality from themethodbot
from common.bot import (
    process_cart_items,
    calculate_fees,
    check_order_limits,
    fetch_order_details,
    track_order_status,
    open_store,
    close_store,
    vouchtop,
    selectedstores
)
from common.check_group_order import process_group_order

# Import budgetbot-specific embed templates
from budgetbot.embed_templates import (
    create_budgetbot_order_summary_embed,
    create_budgetbot_latestsummary_embed,
    create_budgetbot_error_embed,
    create_budgetbot_processing_embed,
    check_order_limits as budgetbot_check_order_limits
)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('budgetbot.log', encoding='utf-8')
    ]
)

logger = logging.getLogger('budgetbot')

# BudgetBot Configuration
DISCORD_BOT_TOKEN = os.getenv('BUDGET_DISCORD_BOT_TOKEN')
DISCORD_GUILD_ID = int(os.getenv('BUDGET_DISCORD_GUILD_ID', '0'))
COMMANDS_GLOBAL = os.getenv('COMMANDS_GLOBAL', 'false').lower() == 'true'

# BudgetBot specific constants
MIN_SUBTOTAL = 20.0  # Same as themethodbot after update
MAX_SUBTOTAL = 35.0  # Same as themethodbot after update

# BudgetBot specific channel IDs (to be configured)
QUEUE_CATEGORY_ID = 1389060752832200745  # Same as themethodbot for now
DELIVERING_CATEGORY_ID = 1389060752832200745  # Same as themethodbot for now

# Validate required environment variables
if not DISCORD_BOT_TOKEN:
    logger.error("BUDGET_DISCORD_BOT_TOKEN not found in environment variables")
    sys.exit(1)

if not DISCORD_GUILD_ID:
    logger.error("BUDGET_DISCORD_GUILD_ID not found in environment variables")
    sys.exit(1)

# Discord bot setup
intents = discord.Intents.default()
intents.message_content = True
intents.guilds = True
intents.members = True

GUILD = discord.Object(id=DISCORD_GUILD_ID)

# Create the bot with optimized settings
bot = commands.Bot(
    command_prefix="!budget",
    intents=intents,
    dm_permission=True,
    case_insensitive=True,
    max_messages=10000,
    heartbeat_timeout=150.0
)

# Command metrics tracking
command_metrics: Dict[str, Dict[str, float]] = {}

# Tracking data storage (shared with themethodbot)
active_tracking_dict: Dict[str, Dict[str, Any]] = {}
clock_data: Dict[str, Any] = {}
status_embed_data: Dict[str, Any] = {}

def track_command_metrics(command_name: str):
    """Decorator to track command usage metrics."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                if command_name not in command_metrics:
                    command_metrics[command_name] = {
                        'total_calls': 0,
                        'total_time': 0.0,
                        'avg_time': 0.0,
                        'last_called': 0.0
                    }
                
                command_metrics[command_name]['total_calls'] += 1
                command_metrics[command_name]['total_time'] += execution_time
                command_metrics[command_name]['avg_time'] = (
                    command_metrics[command_name]['total_time'] / 
                    command_metrics[command_name]['total_calls']
                )
                command_metrics[command_name]['last_called'] = time.time()
                
                logger.info(f"Command '{command_name}' executed in {execution_time:.2f}s")
                return result
            except Exception as e:
                logger.error(f"Error in command '{command_name}': {e}")
                raise
        return wrapper
    return decorator

# Data persistence functions
async def save_tracking_data():
    """Save tracking data to file."""
    try:
        with open('budgetbot_tracking_data.json', 'w') as f:
            json.dump(active_tracking_dict, f, indent=2)
        logger.info("Tracking data saved successfully")
    except Exception as e:
        logger.error(f"Error saving tracking data: {e}")

async def load_tracking_data():
    """Load tracking data from file."""
    global active_tracking_dict
    try:
        if os.path.exists('budgetbot_tracking_data.json'):
            with open('budgetbot_tracking_data.json', 'r') as f:
                active_tracking_dict = json.load(f)
            logger.info(f"Loaded {len(active_tracking_dict)} tracking entries")
            
            # Resume tracking for active orders
            for order_id, tracking_info in active_tracking_dict.items():
                channel_id = tracking_info.get('channel_id')
                if channel_id:
                    channel = bot.get_channel(int(channel_id))
                    if channel:
                        # Resume tracking with budgetbot identifier
                        task = asyncio.create_task(
                            track_order_status(
                                order_id, 
                                channel, 
                                save_tracking_func=save_tracking_data,
                                bot_name="budgetbot"
                            )
                        )
                        bot.tracking_tasks.append(task)
                        logger.info(f"Resumed tracking for order {order_id}")
        else:
            active_tracking_dict = {}
            logger.info("No existing tracking data found")
    except Exception as e:
        logger.error(f"Error loading tracking data: {e}")
        active_tracking_dict = {}

async def save_status_embed_data():
    """Save status embed data to file."""
    try:
        with open('budgetbot_status_embed_data.json', 'w') as f:
            json.dump(status_embed_data, f, indent=2)
        logger.info("Status embed data saved successfully")
    except Exception as e:
        logger.error(f"Error saving status embed data: {e}")

async def load_status_embed_data():
    """Load status embed data from file."""
    global status_embed_data
    try:
        if os.path.exists('budgetbot_status_embed_data.json'):
            with open('budgetbot_status_embed_data.json', 'r') as f:
                status_embed_data = json.load(f)
            logger.info("Status embed data loaded successfully")
        else:
            status_embed_data = {}
            logger.info("No existing status embed data found")
    except Exception as e:
        logger.error(f"Error loading status embed data: {e}")
        status_embed_data = {}

# BudgetBot specific latestsummary function
async def budgetbot_latestsummary(interaction: discord.Interaction, subtotal: float, fees: float, grouporderlink: str = None):
    """BudgetBot version of latestsummary with simplified embed format."""
    try:
        await interaction.response.defer()
        
        # Process the group order link if provided
        result = {}
        cart_items = []
        
        if grouporderlink:
            try:
                result = await process_group_order(grouporderlink)
                if result and 'cart_items' in result:
                    cart_items = result['cart_items']
            except Exception as e:
                logger.error(f"Error processing group order link: {e}")
        
        # Calculate fees using themethodbot's system
        fees_data = {
            'service_fee': 0.0,
            'delivery_fee': 0.0,
            'ca_driver_benefit': 0.0,
            'taxes': fees,
            'uber_one_discount': 0.0
        }
        
        fee_calculations = calculate_fees(fees_data, subtotal, is_cad=False)
        
        # Create budgetbot-specific embed
        embed = create_budgetbot_latestsummary_embed(result, cart_items, fee_calculations)
        
        await interaction.followup.send(embed=embed)
        logger.info(f"BudgetBot latestsummary command executed by {interaction.user}")
        
    except Exception as e:
        logger.error(f"Error in budgetbot_latestsummary: {e}")
        error_embed = create_budgetbot_error_embed("An error occurred while processing your request.")
        await interaction.followup.send(embed=error_embed, ephemeral=True)

@bot.event
async def setup_hook():
    """Set up tasks before the bot starts."""
    # Record start time for uptime tracking
    bot.launch_time = time.time()
    
    # Initialize tracking tasks list
    bot.tracking_tasks = []
    
    # Load saved tracking data and resume tracking
    await load_tracking_data()
    
    # Load status embed data
    await load_status_embed_data()
    
    logger.info("BudgetBot setup completed")

@bot.event
async def on_ready():
    """Called when the bot is ready."""
    logger.info(f"BudgetBot logged in as {bot.user} (ID: {bot.user.id})")
    logger.info(f"Connected to {len(bot.guilds)} guild(s)")
    
    # Sync commands
    try:
        if COMMANDS_GLOBAL:
            synced = await bot.tree.sync()
            logger.info(f"Synced {len(synced)} command(s) globally")
        else:
            bot.tree.copy_global_to(guild=GUILD)
            synced = await bot.tree.sync(guild=GUILD)
            logger.info(f"Synced {len(synced)} command(s) to guild")
    except Exception as e:
        logger.error(f"Failed to sync commands: {e}")

# BudgetBot Commands
@bot.tree.command(
    name="check",
    description="Check a group order link and see the summary (BudgetBot version).",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(grouporderlink="The Uber Eats group order link to check")
async def check_command(interaction: discord.Interaction, grouporderlink: str):
    """BudgetBot version of the check command with simplified embed format."""
    await track_command_metrics("check")(budgetbot_check_order_limits)(interaction, grouporderlink)

@bot.tree.command(
    name="latestsummary",
    description="Create a budget-friendly order summary (BudgetBot version).",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(
    subtotal="Enter the original total amount (e.g., 21.28)",
    fees="Enter the fees and tax amount (e.g., 5.99)",
    grouporderlink="(Optional) Group order link to fetch location and items"
)
async def latestsummary_command(
    interaction: discord.Interaction,
    subtotal: float,
    fees: float,
    grouporderlink: str = None
):
    """BudgetBot wrapper for latestsummary function."""
    await track_command_metrics("latestsummary")(budgetbot_latestsummary)(interaction, subtotal, fees, grouporderlink)

@bot.tree.command(
    name="track",
    description="Track an order status (BudgetBot version).",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(order_id="The order ID to track")
async def track_command(interaction: discord.Interaction, order_id: str):
    """BudgetBot version of order tracking with persistent embed system."""
    try:
        await interaction.response.defer()

        # Start tracking with budgetbot identifier
        task = asyncio.create_task(
            track_order_status(
                order_id,
                interaction.channel,
                save_tracking_func=save_tracking_data,
                bot_name="budgetbot"
            )
        )
        bot.tracking_tasks.append(task)

        # Store tracking info
        active_tracking_dict[order_id] = {
            'channel_id': str(interaction.channel.id),
            'started_at': time.time(),
            'bot_name': 'budgetbot'
        }
        await save_tracking_data()

        embed = discord.Embed(
            title="🔍 BudgetBot Order Tracking Started",
            description=f"Now tracking order: `{order_id}`",
            color=BUDGETBOT_PRIMARY_COLOR
        )
        embed.set_footer(text="BudgetBot | Order Tracking")

        await interaction.followup.send(embed=embed)
        logger.info(f"BudgetBot tracking started for order {order_id} by {interaction.user}")

    except Exception as e:
        logger.error(f"Error in BudgetBot track command: {e}")
        error_embed = create_budgetbot_error_embed("An error occurred while starting order tracking.")
        await interaction.followup.send(embed=error_embed, ephemeral=True)

@bot.tree.command(
    name="open",
    description="Open the budget store for orders.",
    guild=None if COMMANDS_GLOBAL else GUILD
)
async def open_store_command(interaction: discord.Interaction):
    """BudgetBot version of store opening with persistent embed system."""
    try:
        # Use the common open_store function with budgetbot identifier
        await track_command_metrics("open_store")(open_store)(interaction, bot_name="budgetbot")
        logger.info(f"BudgetBot store opened by {interaction.user}")
    except Exception as e:
        logger.error(f"Error in BudgetBot open command: {e}")
        error_embed = create_budgetbot_error_embed("An error occurred while opening the store.")
        await interaction.response.send_message(embed=error_embed, ephemeral=True)

@bot.tree.command(
    name="close",
    description="Close the budget store for orders.",
    guild=None if COMMANDS_GLOBAL else GUILD
)
async def close_store_command(interaction: discord.Interaction):
    """BudgetBot version of store closing with persistent embed system."""
    try:
        # Use the common close_store function with budgetbot identifier
        await track_command_metrics("close_store")(close_store)(interaction, bot_name="budgetbot")
        logger.info(f"BudgetBot store closed by {interaction.user}")
    except Exception as e:
        logger.error(f"Error in BudgetBot close command: {e}")
        error_embed = create_budgetbot_error_embed("An error occurred while closing the store.")
        await interaction.response.send_message(embed=error_embed, ephemeral=True)

def run_bot():
    """Run the BudgetBot."""
    try:
        bot.run(DISCORD_BOT_TOKEN, log_handler=None)
    except KeyboardInterrupt:
        logger.info("BudgetBot stopped by keyboard interrupt")
    except Exception as e:
        logger.error(f"Error running BudgetBot: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    run_bot()

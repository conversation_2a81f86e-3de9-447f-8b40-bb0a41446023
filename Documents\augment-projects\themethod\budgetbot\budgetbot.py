#!/usr/bin/env python3
"""
Generic Discord Bot - Consolidated self-contained bot for order processing
Features:
- Automatic group order link detection and processing
- Basic order tracking functionality
- All embed templates and fee calculation logic included inline
"""

import discord
from discord.ext import commands
from discord import app_commands
import logging
import os
from dotenv import load_dotenv
import re
import asyncio
import traceback
import time
import aiohttp
import json
import datetime
from typing import Dict, Optional, Any, List

import sys
import os

# Import essential functions only from common modules
from common.bot import (
    extract_group_link,
    fetch_order_details,
    track_order_status
)
from common.check_group_order import process_group_order

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('bot.log', encoding='utf-8')
    ]
)

# Create a logger for this module
logger = logging.getLogger('generic_bot')

# Get environment variables
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN")
DISCORD_GUILD_ID = int(os.getenv("DISCORD_GUILD_ID"))
COMMANDS_GLOBAL = False

# Set up bot
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.message_content = True
intents.dm_messages = True

# Create the bot
bot = commands.Bot(
    command_prefix="!bot",
    intents=intents,
    dm_permission=True,
    case_insensitive=True
)

GUILD = discord.Object(id=DISCORD_GUILD_ID)

# Global session for HTTP requests
_http_session: Optional[aiohttp.ClientSession] = None

# Active tracking information (minimal)
active_tracking: Dict[str, Dict[str, Any]] = {}

# ============================================================================
# INLINE EMBED TEMPLATES (consolidated from embed_templates.py)
# ============================================================================

def create_locked_order_embed():
    """Create a generic embed for locked/canceled orders."""
    embed = discord.Embed(
        title="🔒 Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.red()
    )

    embed.add_field(
        name="Step 1",
        value="Go back to your cart",
        inline=False
    )

    embed.add_field(
        name="Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    return embed

def create_order_summary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a generic order summary embed."""
    embed = discord.Embed(
        title="🍔 Order Summary",
        color=discord.Color.blue()
    )

    # Add group order link if available
    group_link = result.get('group_link', 'Not available')
    embed.description = f"**[Group Order Link]({group_link})**\n\n"

    # Location
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="📍 Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="🏪 Restaurant",
            value=f"[View Restaurant]({store_url})",
            inline=False
        )

    # Add cart items
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Add subtotal
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"
    subtotal = fee_calculations.get('subtotal', 0)

    embed.add_field(
        name="💰 Subtotal",
        value=f"`{currency}{subtotal:.2f}`",
        inline=False
    )

    embed.set_footer(text="Order Processing Bot")

    return embed

def create_error_embed(error_message: str) -> discord.Embed:
    """Create a generic error embed."""
    embed = discord.Embed(
        title="❌ Error Processing Order",
        description=f"An error occurred while processing your order:\n\n```{error_message}```",
        color=discord.Color.red()
    )

    # Add troubleshooting steps
    embed.add_field(
        name="🔧 Troubleshooting Steps",
        value="1. Make sure your group order is unlocked\n" +
              "2. Try creating a new group order\n" +
              "3. Check that the link is valid",
        inline=False
    )

    embed.set_footer(text="If the issue persists, please try again later")

    return embed

def create_processing_embed() -> discord.Embed:
    """Create a generic processing embed."""
    embed = discord.Embed(
        title="🔄 Processing Order",
        description="Analyzing your group order. This may take a few seconds...",
        color=discord.Color.blurple()
    )

    return embed

# ============================================================================
# INLINE FEE CALCULATOR (consolidated from fee_calculator.py)
# ============================================================================

def calculate_fees_without_delivery(fees_data: Dict[str, Any], subtotal: float, is_cad: bool = False) -> Dict[str, Any]:
    """Calculate basic fee-related values, excluding delivery fee."""
    # Use the provided subtotal parameter
    actual_subtotal = subtotal

    # Extract fees from fees_data
    service_fee = float(fees_data.get('service_fee', 0))
    ca_driver_benefit = float(fees_data.get('ca_driver_benefit', 0))
    taxes = float(fees_data.get('taxes', 0))
    uber_one_discount = float(fees_data.get('uber_one_discount', 0))

    # Calculate total fees (excluding delivery fee)
    total_fees = round(service_fee + ca_driver_benefit + taxes, 2)
    final_fees = round(total_fees - uber_one_discount, 2)

    # Calculate final total
    final_total = round(actual_subtotal + final_fees, 2)

    # Return calculated values
    return {
        'subtotal': actual_subtotal,
        'service_fee': service_fee,
        'ca_driver_benefit': ca_driver_benefit,
        'taxes': taxes,
        'uber_one_discount': uber_one_discount,
        'total_fees': total_fees,
        'final_fees': final_fees,
        'final_total': final_total,
        'is_cad': is_cad
    }

# ============================================================================
# BOT HELPER FUNCTIONS
# ============================================================================

async def get_session() -> aiohttp.ClientSession:
    """Get or create the global HTTP session."""
    global _http_session
    if _http_session is None or _http_session.closed:
        _http_session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=20, ttl_dns_cache=300)
        )
    return _http_session

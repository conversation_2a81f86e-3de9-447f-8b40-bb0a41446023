#!/usr/bin/env python3
"""
Discord Token Validation Script
Helps validate and identify Discord bot tokens to resolve authentication issues.
"""

import asyncio
import aiohttp
import base64
import json
import os
from dotenv import load_dotenv

async def validate_discord_token(token):
    """Validate a Discord bot token and get bot information."""
    print(f"🔍 Validating token: {token[:20]}...")
    
    headers = {
        'Authorization': f'Bot {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test 1: Get bot user information
            print("\n📝 Test 1: Getting bot user information...")
            async with session.get('https://discord.com/api/v10/users/@me', headers=headers) as response:
                if response.status == 200:
                    bot_data = await response.json()
                    print(f"   ✅ SUCCESS: Token is valid!")
                    print(f"   🤖 Bot Name: {bot_data.get('username')}#{bot_data.get('discriminator')}")
                    print(f"   🆔 Bot ID: {bot_data.get('id')}")
                    print(f"   ✅ Bot Status: {'Verified' if bot_data.get('verified') else 'Unverified'}")
                    print(f"   🔧 Bot Type: {'Public' if bot_data.get('public_bot') else 'Private'}")
                    
                    return True, bot_data
                elif response.status == 401:
                    error_data = await response.json()
                    print(f"   ❌ AUTHENTICATION FAILED: {error_data.get('message', 'Invalid token')}")
                    return False, None
                else:
                    print(f"   ❌ HTTP ERROR: {response.status} - {await response.text()}")
                    return False, None
                    
    except Exception as e:
        print(f"   ❌ CONNECTION ERROR: {e}")
        return False, None

def decode_token_info(token):
    """Decode basic information from Discord token structure."""
    print("🔍 Analyzing token structure...")
    
    try:
        # Discord tokens have format: [user_id_base64].[timestamp_base64].[hmac]
        parts = token.split('.')
        if len(parts) != 3:
            print("   ❌ Invalid token format (should have 3 parts separated by dots)")
            return
        
        # Decode the first part (user ID)
        try:
            user_id_bytes = base64.b64decode(parts[0] + '==')  # Add padding
            user_id = int.from_bytes(user_id_bytes, 'big')
            print(f"   🆔 Encoded Bot ID: {user_id}")
        except Exception as e:
            print(f"   ❌ Could not decode bot ID: {e}")
        
        # Decode the second part (timestamp)
        try:
            timestamp_bytes = base64.b64decode(parts[1] + '==')  # Add padding
            timestamp = int.from_bytes(timestamp_bytes, 'big')
            print(f"   ⏰ Token Timestamp: {timestamp}")
        except Exception as e:
            print(f"   ❌ Could not decode timestamp: {e}")
            
        print(f"   📏 Token Length: {len(token)} characters")
        print(f"   🔧 Token Parts: {len(parts[0])}, {len(parts[1])}, {len(parts[2])} characters")
        
    except Exception as e:
        print(f"   ❌ Error analyzing token: {e}")

async def main():
    """Main validation function."""
    print("🔧 DISCORD TOKEN VALIDATION")
    print("=" * 60)
    
    # Load environment variables
    load_dotenv(override=True)
    
    # Get the current token
    current_token = os.getenv('BUDGET_DISCORD_BOT_TOKEN')
    
    if not current_token:
        print("❌ BUDGET_DISCORD_BOT_TOKEN not found in environment variables")
        return
    
    print(f"📍 Current working directory: {os.getcwd()}")
    print(f"🔑 Token from .env file: {current_token[:20]}...{current_token[-10:]}")
    
    # Analyze token structure
    decode_token_info(current_token)
    
    # Validate token with Discord API
    print("\n" + "="*60)
    is_valid, bot_data = await validate_discord_token(current_token)
    
    print("\n📊 VALIDATION RESULTS")
    print("=" * 30)
    
    if is_valid and bot_data:
        bot_name = f"{bot_data.get('username')}#{bot_data.get('discriminator')}"
        print(f"✅ Token Status: VALID")
        print(f"🤖 Bot Identity: {bot_name}")
        print(f"🆔 Bot ID: {bot_data.get('id')}")
        
        # Check if this is the expected "Quick Eats" bot
        if "Quick Eats" in bot_data.get('username', ''):
            print("🎉 SUCCESS: This token belongs to the 'Quick Eats' bot!")
        elif "Budget Bites" in bot_data.get('username', ''):
            print("⚠️  WARNING: This token belongs to 'Budget Bites HQ', not 'Quick Eats'")
        else:
            print(f"⚠️  WARNING: This token belongs to '{bot_data.get('username')}', not 'Quick Eats'")
            
        print("\n🚀 NEXT STEPS:")
        print("   1. If this is the correct bot, BudgetBot should work")
        print("   2. If this is the wrong bot, get the correct 'Quick Eats' token")
        print("   3. Update the .env file with the correct token")
        
    else:
        print("❌ Token Status: INVALID")
        print("\n🔧 TROUBLESHOOTING:")
        print("   1. Check if the token was copied correctly (no extra spaces/characters)")
        print("   2. Verify the token hasn't expired or been regenerated")
        print("   3. Ensure the token belongs to the correct 'Quick Eats' bot")
        print("   4. Check Discord Developer Portal for the correct token")
        
        print("\n📋 TO FIX:")
        print("   1. Go to Discord Developer Portal (https://discord.com/developers/applications)")
        print("   2. Find the 'Quick Eats' bot application")
        print("   3. Go to Bot section and copy the token")
        print("   4. Update BUDGET_DISCORD_BOT_TOKEN in .env file")

if __name__ == "__main__":
    asyncio.run(main())

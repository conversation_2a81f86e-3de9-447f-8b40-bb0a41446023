#!/usr/bin/env python3
"""
Verification script to confirm admin role ID has been corrected in credits system commands.
"""

import os
import sys

def verify_admin_role_fix():
    """Verify that admin role ID has been corrected in all credits system commands."""
    
    print("🔧 Verifying Admin Role ID Fix for Credits System")
    print("=" * 60)
    
    # Check if the main bot file exists
    bot_file = "themethodbot/themethodbot.py"
    if not os.path.exists(bot_file):
        print("❌ Bot file not found!")
        return False
    
    # Read the bot file
    with open(bot_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for old incorrect role ID
    old_role_id = "1389516482341240893"
    correct_role_id = "1340213865748762634"
    
    old_role_count = content.count(old_role_id)
    correct_role_count = content.count(correct_role_id)
    
    print(f"🔍 Checking for old role ID ({old_role_id})...")
    if old_role_count == 0:
        print("✅ No instances of old role ID found")
    else:
        print(f"❌ Found {old_role_count} instances of old role ID")
        return False
    
    print(f"\n🔍 Checking for correct role ID ({correct_role_id})...")
    print(f"✅ Found {correct_role_count} instances of correct role ID")
    
    # Specifically check credits system commands
    credits_commands = [
        ("/creditcheck command", 'name="creditcheck"'),
        ("/removecredits command", 'name="removecredits"')
    ]
    
    print(f"\n📋 Verifying Credits System Commands:")
    print("-" * 40)
    
    all_good = True
    for command_name, search_pattern in credits_commands:
        if search_pattern in content:
            # Find the command and check its role ID
            start_pos = content.find(search_pattern)
            # Look for the role check within 500 characters after the command
            command_section = content[start_pos:start_pos + 500]
            
            if correct_role_id in command_section:
                print(f"✅ {command_name} - Using correct role ID")
            elif old_role_id in command_section:
                print(f"❌ {command_name} - Still using old role ID")
                all_good = False
            else:
                print(f"⚠️  {command_name} - Role ID not found in expected location")
                all_good = False
        else:
            print(f"❌ {command_name} - Command not found")
            all_good = False
    
    print("\n" + "=" * 60)
    
    if all_good and old_role_count == 0:
        print("🎉 SUCCESS: Admin role ID has been corrected!")
        print(f"✅ Old role ID ({old_role_id}) completely removed")
        print(f"✅ Correct role ID ({correct_role_id}) in use")
        print("✅ Both /creditcheck and /removecredits commands updated")
        
        print(f"\n📊 SUMMARY:")
        print(f"• Correct role ID instances: {correct_role_count}")
        print(f"• Old role ID instances: {old_role_count}")
        print(f"• Credits system commands verified: 2/2")
        
        return True
    else:
        print("❌ FAILED: Issues found with admin role ID")
        return False

def show_role_info():
    """Display information about the role ID correction."""
    
    print("\n📋 ADMIN ROLE ID CORRECTION DETAILS")
    print("=" * 60)
    
    print("🔴 INCORRECT (Old): 1389516482341240893")
    print("🟢 CORRECT (New):   1340213865748762634")
    
    print(f"\n📝 COMMANDS UPDATED:")
    print("• /creditcheck - Check any user's credit balance (Admin only)")
    print("• /removecredits - Remove credits from user's balance (Admin only)")
    
    print(f"\n🔧 TECHNICAL DETAILS:")
    print("• Role check decorator: @app_commands.checks.has_any_role(1340213865748762634)")
    print("• Both commands now use the correct themethodbot admin role")
    print("• Role validation occurs before command execution")
    print("• Ephemeral responses maintain admin privacy")

def main():
    """Main verification function."""
    print("🛠️  TheMethod Credits System - Admin Role ID Verification")
    print("=" * 80)
    
    # Verify the fix
    fix_successful = verify_admin_role_fix()
    
    if fix_successful:
        show_role_info()
        
        print("\n" + "=" * 80)
        print("✅ VERIFICATION COMPLETE - Admin role ID has been corrected!")
        print("🎯 Credits system commands now use the correct admin role.")
        print("💡 Test the commands in Discord to confirm they work properly.")
        return 0
    else:
        print("\n" + "=" * 80)
        print("❌ VERIFICATION FAILED - Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

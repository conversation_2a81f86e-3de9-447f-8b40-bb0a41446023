#!/usr/bin/env python3
"""
Test script to verify the three specific changes made to themethodbot:
1. Fix /ordersuccess Channel Movement
2. Remove Order Status Button  
3. Update Embed Title
"""

import os
import re

def test_ordersuccess_channel_movement():
    """Test that /ordersuccess channel movement is correctly implemented."""
    print("🔍 Testing /ordersuccess Channel Movement...")
    
    themethodbot_file = "themethodbot/themethodbot.py"
    
    if not os.path.exists(themethodbot_file):
        print(f"❌ File not found: {themethodbot_file}")
        return False
    
    with open(themethodbot_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for correct DELIVERING_CATEGORY_ID
    if "DELIVERING_CATEGORY_ID = 1354242418211422419" not in content:
        print("❌ DELIVERING_CATEGORY_ID not set to correct value")
        return False
    
    # Check for move_to_delivering function call in ordersuccess
    if "await move_to_delivering(current_channel)" not in content:
        print("❌ move_to_delivering function not called in ordersuccess")
        return False
    
    # Check for improved move_to_delivering function with position validation
    if "if position is not None and not isinstance(position, int):" not in content:
        print("❌ Position validation not implemented in move_to_delivering")
        return False
    
    print("✅ /ordersuccess channel movement correctly implemented")
    return True

def test_order_status_button_removal():
    """Test that Order Status button has been removed from OrderStatusButtons."""
    print("\n🔍 Testing Order Status Button Removal...")
    
    common_bot_file = "common/bot.py"
    
    if not os.path.exists(common_bot_file):
        print(f"❌ File not found: {common_bot_file}")
        return False
    
    with open(common_bot_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check that OrderStatusButtons class exists
    if "class OrderStatusButtons(discord.ui.View):" not in content:
        print("❌ OrderStatusButtons class not found")
        return False
    
    # Check that only Status History button exists
    status_history_button_count = content.count('@discord.ui.button(label="📋 Status History"')
    if status_history_button_count != 1:
        print(f"❌ Expected 1 Status History button, found {status_history_button_count}")
        return False
    
    # Check that no "Order Status" button exists
    if "Order Status" in content and "button" in content.lower():
        # More specific check - look for button with "Order Status" label
        order_status_button_pattern = r'@discord\.ui\.button\([^)]*label="[^"]*Order Status[^"]*"'
        if re.search(order_status_button_pattern, content):
            print("❌ Order Status button still exists")
            return False
    
    print("✅ Order Status button successfully removed, only Status History button remains")
    return True

def test_embed_title_update():
    """Test that embed titles have been updated correctly."""
    print("\n🔍 Testing Embed Title Updates...")
    
    files_to_check = [
        ("themethodbot/themethodbot.py", "themethodbot"),
        ("common/bot.py", "common/bot")
    ]
    
    success = True
    
    for file_path, file_name in files_to_check:
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            success = False
            continue
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check that old title is removed
        if "🚗 Order Status Update" in content:
            print(f"❌ Old title '🚗 Order Status Update' still found in {file_name}")
            success = False
        
        # Check that new title exists
        new_title_count = content.count("<:car:1360177292730568865> Order Tracking")
        if file_name == "themethodbot":
            # Should have 2 instances (in /track and /ordersuccess initial embeds)
            if new_title_count < 2:
                print(f"❌ New title '<:car:1360177292730568865> Order Tracking' found {new_title_count} times in {file_name}, expected at least 2")
                success = False
        elif file_name == "common/bot":
            # Should have 1 instance (in track_order_status function)
            if new_title_count < 1:
                print(f"❌ New title '<:car:1360177292730568865> Order Tracking' found {new_title_count} times in {file_name}, expected at least 1")
                success = False
    
    if success:
        print("✅ Embed titles successfully updated to '<:car:1360177292730568865> Order Tracking'")
    
    return success

def test_code_structure():
    """Test overall code structure and imports."""
    print("\n🔍 Testing Code Structure...")
    
    themethodbot_file = "themethodbot/themethodbot.py"
    
    if not os.path.exists(themethodbot_file):
        print(f"❌ File not found: {themethodbot_file}")
        return False
    
    with open(themethodbot_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for proper imports
    required_imports = [
        "from common.bot import OrderStatusButtons",
        "track_order_status"  # Should be in the import block
    ]
    
    for import_stmt in required_imports:
        if import_stmt not in content:
            print(f"❌ Missing import: {import_stmt}")
            return False
    
    print("✅ Code structure and imports are correct")
    return True

def main():
    """Run all tests."""
    print("🚀 Starting Three Changes Verification Tests")
    
    # Run all tests
    test1_result = test_ordersuccess_channel_movement()
    test2_result = test_order_status_button_removal()
    test3_result = test_embed_title_update()
    test4_result = test_code_structure()
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    results = [
        ("✅ PASS" if test1_result else "❌ FAIL", "/ordersuccess Channel Movement"),
        ("✅ PASS" if test2_result else "❌ FAIL", "Order Status Button Removal"),
        ("✅ PASS" if test3_result else "❌ FAIL", "Embed Title Updates"),
        ("✅ PASS" if test4_result else "❌ FAIL", "Code Structure")
    ]
    
    for status, test_name in results:
        print(f"{status} - {test_name}")
    
    passed_tests = sum([test1_result, test2_result, test3_result, test4_result])
    total_tests = 4
    
    print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Three changes implemented successfully.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit(main())

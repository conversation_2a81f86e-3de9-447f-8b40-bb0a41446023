#!/usr/bin/env python3
"""
Test script to verify that promo checking functionality has been successfully removed
and that order summary embeds are always generated regardless of store promo status.
"""

import asyncio
import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_process_group_order():
    """Test that process_group_order always returns is_promo=True"""
    try:
        from common.check_group_order import process_group_order
        
        # Test with a sample group order link (this won't actually work without valid cookies)
        # but we can test that the function structure is correct
        test_link = "https://eats.uber.com/group-orders/test-uuid-12345"
        
        logger.info("Testing process_group_order function...")
        logger.info(f"Test link: {test_link}")
        
        # This will likely fail due to invalid link/cookies, but we can check the structure
        result = await process_group_order(test_link)
        
        if result is None:
            logger.info("✅ process_group_order returned None (expected for invalid link)")
        else:
            is_promo = result.get('is_promo', False)
            logger.info(f"✅ process_group_order returned is_promo: {is_promo}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing process_group_order: {str(e)}")
        return False

def test_embed_templates():
    """Test that embed templates always show promo status as active"""
    try:
        from themethodbot.embed_templates import create_order_summary_embed
        
        logger.info("Testing embed templates...")
        
        # Create test data
        test_result = {
            'group_link': 'https://eats.uber.com/group-orders/test-12345',
            'location': {
                'address': '123 Test St',
                'city': 'Test City',
                'state': 'TS',
                'zipcode': '12345'
            },
            'is_promo': False  # This should be ignored now
        }
        
        test_cart_items = [
            "Test Item 1 x1 ($10.00)",
            "Test Item 2 x2 ($15.00)"
        ]
        
        test_fee_calculations = {
            'subtotal': 40.00,
            'discounted_subtotal': 12.00,
            'savings': 28.00,
            'overflow_fee': 0,
            'service_fee': 3.00,
            'taxes': 2.50,
            'total_fees': 5.50,
            'final_total': 17.50,
            'is_cad': False
        }
        
        # Create embed
        embed = create_order_summary_embed(test_result, test_cart_items, test_fee_calculations)
        
        # Check that the embed description contains promo status as active
        if embed.description and "Store is in promo!" in embed.description:
            logger.info("✅ Embed template shows store as in promo (correct)")
            return True
        else:
            logger.error("❌ Embed template does not show store as in promo")
            logger.info(f"Embed description: {embed.description}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing embed templates: {str(e)}")
        return False

def test_promo_functions_removed():
    """Test that promo checking functions are no longer called in main flow"""
    try:
        logger.info("Testing that promo checking functions are bypassed...")
        
        # Check that the main files have been modified correctly
        files_to_check = [
            'common/check_group_order.py',
            'common/bot.py',
            'themethodbot/themethodbot.py',
            'themethodbot/embed_templates.py'
        ]
        
        for file_path in files_to_check:
            full_path = os.path.join(os.path.dirname(__file__), file_path)
            if os.path.exists(full_path):
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # Check for removal indicators
                if "Promo checking removed" in content or "always treat stores as having valid promotions" in content:
                    logger.info(f"✅ {file_path}: Contains promo removal indicators")
                elif "is_promo = True" in content and "check_store_promo" not in content:
                    logger.info(f"✅ {file_path}: Promo checking bypassed correctly")
                else:
                    logger.warning(f"⚠️ {file_path}: May still contain promo checking logic")
            else:
                logger.warning(f"⚠️ File not found: {file_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error checking promo function removal: {str(e)}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 Starting promo removal verification tests...")
    logger.info("=" * 60)
    
    tests = [
        ("Process Group Order Test", test_process_group_order()),
        ("Embed Templates Test", test_embed_templates()),
        ("Promo Functions Removal Test", test_promo_functions_removed())
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        logger.info("-" * 40)
        
        if asyncio.iscoroutine(test_func):
            result = await test_func
        else:
            result = test_func
            
        results.append((test_name, result))
        
        if result:
            logger.info(f"✅ {test_name}: PASSED")
        else:
            logger.error(f"❌ {test_name}: FAILED")
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Promo removal appears to be successful.")
        return True
    else:
        logger.error("⚠️ Some tests failed. Please review the results above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)

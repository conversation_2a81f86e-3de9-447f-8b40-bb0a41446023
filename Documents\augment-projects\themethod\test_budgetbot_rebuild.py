#!/usr/bin/env python3
"""
Test script to verify the rebuilt BudgetBot functionality.

This script verifies that BudgetBot has been successfully rebuilt with:
1. Complete code reset and cleanup
2. Order checker function copied from themethodbot
3. /ordersuccess command copied from themethodbot
4. BudgetBot-specific styling and branding
5. Simplified structure with only essential functionality
"""

import sys
import os
import re
import importlib.util

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_budgetbot_structure():
    """Test that BudgetBot has the correct rebuilt structure."""
    print("🧪 Testing BudgetBot Rebuilt Structure")
    print("=" * 50)
    
    try:
        with open('budgetbot/budgetbot.py', 'r', encoding='utf-8') as f:
            budgetbot_content = f.read()
    except FileNotFoundError:
        print("   ❌ budgetbot/budgetbot.py not found")
        return False
    
    # Test for essential imports and setup
    essential_patterns = [
        r'import discord',
        r'import aiohttp',
        r'from common\.check_group_order import process_group_order',
        r'fetch_order_details',
        r'track_order_status',
        r'BUDGETBOT_PRIMARY_COLOR.*255, 165, 0',  # Orange theme
        r'async def get_session\(\)',
        r'async def move_to_delivering\(',
        r'active_tracking.*Dict\[str, Dict\[str, Any\]\]'
    ]
    
    structure_success = True
    for pattern in essential_patterns:
        if re.search(pattern, budgetbot_content):
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            structure_success = False
    
    return structure_success

def test_order_checker_function():
    """Test that the order checker function is properly implemented."""
    print("\n🧪 Testing Order Checker Function")
    print("=" * 50)
    
    try:
        with open('budgetbot/budgetbot.py', 'r', encoding='utf-8') as f:
            budgetbot_content = f.read()
    except FileNotFoundError:
        print("   ❌ budgetbot/budgetbot.py not found")
        return False
    
    # Test for order checker implementation
    checker_patterns = [
        r'name="checklink"',
        r'async def checklink_command',
        r'await process_group_order\(group_order_link\)',
        r'LOCKED_ORDER',
        r'title="🍔 BudgetBot Order Summary"',
        r'color=BUDGETBOT_PRIMARY_COLOR',
        r'📍 Delivery Location',
        r'🏪 Restaurant',
        r'🛒 Order Items',
        r'💰 Subtotal',
        r'BudgetBot \| Budget-Friendly Order Summary'
    ]
    
    checker_success = True
    for pattern in checker_patterns:
        if re.search(pattern, budgetbot_content):
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            checker_success = False
    
    return checker_success

def test_ordersuccess_command():
    """Test that the /ordersuccess command is properly implemented."""
    print("\n🧪 Testing /ordersuccess Command")
    print("=" * 50)
    
    try:
        with open('budgetbot/budgetbot.py', 'r', encoding='utf-8') as f:
            budgetbot_content = f.read()
    except FileNotFoundError:
        print("   ❌ budgetbot/budgetbot.py not found")
        return False
    
    # Test for ordersuccess implementation
    ordersuccess_patterns = [
        r'name="ordersuccess"',
        r'async def ordersuccess_slash',
        r'BudgetBot.*ordersuccess.*was triggered',
        r'move_to_delivering\(current_channel\)',
        r'await fetch_order_details\(order_id, session\)',
        r'title="🎉 BudgetBot Order Tracking Initiated"',
        r'color=BUDGETBOT_PRIMARY_COLOR',
        r'BudgetBot Order Tracking',
        r'track_order_status.*bot_name=.budgetbot.',
        r'Order is being tracked automatically \| BudgetBot'
    ]
    
    ordersuccess_success = True
    for pattern in ordersuccess_patterns:
        if re.search(pattern, budgetbot_content):
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            ordersuccess_success = False
    
    return ordersuccess_success

def test_removed_functionality():
    """Test that unwanted functionality has been removed."""
    print("\n🧪 Testing Removed Functionality")
    print("=" * 50)
    
    try:
        with open('budgetbot/budgetbot.py', 'r', encoding='utf-8') as f:
            budgetbot_content = f.read()
    except FileNotFoundError:
        print("   ❌ budgetbot/budgetbot.py not found")
        return False
    
    # Test that automatic link detection is removed
    removed_patterns = [
        r'detect_order_link',
        r'process_detected_order_link',
        r'on_message.*async def',
        r'BUDGETBOT_LINK_PATTERNS',
        r'auto_link_detection',
        r'BUDGETBOT_EMOJIS',
        r'BUDGETBOT_FEATURES'
    ]
    
    removal_success = True
    for pattern in removed_patterns:
        if re.search(pattern, budgetbot_content):
            print(f"   ❌ Found (should be removed): {pattern}")
            removal_success = False
        else:
            print(f"   ✅ Removed: {pattern}")
    
    return removal_success

def test_budgetbot_imports():
    """Test that BudgetBot can be imported successfully."""
    print("\n🧪 Testing BudgetBot Import")
    print("=" * 50)
    
    try:
        spec = importlib.util.find_spec('budgetbot.budgetbot')
        if spec is not None:
            print("   ✅ BudgetBot module can be imported")
            return True
        else:
            print("   ❌ BudgetBot module cannot be found")
            return False
    except Exception as e:
        print(f"   ❌ Import error: {e}")
        return False

def test_command_count():
    """Test that BudgetBot has exactly 2 commands."""
    print("\n🧪 Testing Command Count")
    print("=" * 50)
    
    try:
        with open('budgetbot/budgetbot.py', 'r', encoding='utf-8') as f:
            budgetbot_content = f.read()
    except FileNotFoundError:
        print("   ❌ budgetbot/budgetbot.py not found")
        return False
    
    # Count @bot.tree.command occurrences
    command_matches = re.findall(r'@bot\.tree\.command', budgetbot_content)
    command_count = len(command_matches)
    
    if command_count == 2:
        print(f"   ✅ Correct number of commands: {command_count}")
        print("   ✅ Commands should be: /checklink and /ordersuccess")
        return True
    else:
        print(f"   ❌ Incorrect number of commands: {command_count} (expected: 2)")
        return False

def main():
    """Run all BudgetBot rebuild verification tests."""
    print("🔧 BUDGETBOT REBUILD VERIFICATION")
    print("=" * 70)
    print("Testing BudgetBot rebuild with specific functionality copied from themethodbot:")
    print("• Complete code reset and cleanup")
    print("• Order checker function from themethodbot")
    print("• /ordersuccess command from themethodbot")
    print("• BudgetBot-specific styling and branding")
    print("• Simplified structure with only essential functionality")
    print()
    
    # Run all tests
    tests = [
        ("Rebuilt Structure", test_budgetbot_structure),
        ("Order Checker Function", test_order_checker_function),
        ("/ordersuccess Command", test_ordersuccess_command),
        ("Removed Functionality", test_removed_functionality),
        ("Import Capability", test_budgetbot_imports),
        ("Command Count", test_command_count)
    ]
    
    results = []
    for test_name, test_func in tests:
        results.append((test_name, test_func()))
    
    # Summary
    print("\n📊 VERIFICATION RESULTS")
    print("=" * 50)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        emoji = "✅" if result else "❌"
        print(f"{emoji} {test_name}: {status}")
    
    if all(result for _, result in results):
        print("\n🎉 BUDGETBOT REBUILD SUCCESSFULLY COMPLETED!")
        print("\n🚀 Final Structure:")
        print("   • Basic bot setup with orange theme (255, 165, 0)")
        print("   • /checklink command - copied from themethodbot with BudgetBot styling")
        print("   • /ordersuccess command - copied from themethodbot with BudgetBot branding")
        print("   • Simplified order summary embeds (Location, Restaurant, Items, Subtotal)")
        print("   • Helper functions: get_session(), move_to_delivering()")
        print("   • Connects as 'Quick Eats' bot")
        print("   • No automatic link detection or complex features")
        print("\n📋 Ready for Discord testing!")
        return True
    else:
        print("\n❌ SOME TESTS FAILED - PLEASE REVIEW")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

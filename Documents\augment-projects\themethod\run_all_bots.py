import logging
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('themethodbot.log', encoding='utf-8')
    ]
)

logger = logging.getLogger('run_themethodbot')

def run_themethodbot():
    """Run The Method Bot."""
    try:
        from themethodbot import themethodbot
        logger.info("Starting themethodbot...")
        themethodbot.run_bot()
    except Exception as e:
        logger.error(f"Error running themethodbot: {e}")
        logger.error(traceback.format_exc())

def run_budgetbot():
    """Run Budget Bot."""
    try:
        from budgetbot import budgetbot
        logger.info("Starting budgetbot...")
        budgetbot.run_bot()
    except Exception as e:
        logger.error(f"Error running budgetbot: {e}")
        logger.error(traceback.format_exc())

def run_all_bots():
    """Run both The Method Bot and Budget Bot."""
    logger.info("Starting both bots...")
    import multiprocessing
    import time

    try:
        # Create processes for both bots
        themethodbot_process = multiprocessing.Process(target=run_themethodbot, name="themethodbot")
        budgetbot_process = multiprocessing.Process(target=run_budgetbot, name="budgetbot")

        # Start both processes
        logger.info("Starting themethodbot process...")
        themethodbot_process.start()

        time.sleep(2)  # Small delay between starts

        logger.info("Starting budgetbot process...")
        budgetbot_process.start()

        # Wait for both processes
        themethodbot_process.join()
        budgetbot_process.join()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down bots...")
        if 'themethodbot_process' in locals() and themethodbot_process.is_alive():
            themethodbot_process.terminate()
        if 'budgetbot_process' in locals() and budgetbot_process.is_alive():
            budgetbot_process.terminate()
    except Exception as e:
        logger.error(f"Error running bots: {e}")
        logger.error(traceback.format_exc())

    logger.info("All bots have been shut down.")

if __name__ == "__main__":
    run_all_bots()

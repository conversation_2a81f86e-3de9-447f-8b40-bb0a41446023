import logging
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('themethodbot.log', encoding='utf-8')
    ]
)

logger = logging.getLogger('run_themethodbot')

def run_themethodbot():
    """Run The Method Bot."""
    try:
        from themethodbot import themethodbot
        logger.info("Starting themethodbot...")
        themethodbot.run_bot()
    except Exception as e:
        logger.error(f"Error running themethodbot: {e}")
        logger.error(traceback.format_exc())

def run_all_bots():
    """Run The Method Bot (only bot remaining)."""
    logger.info("Starting The Method Bot...")
    try:
        run_themethodbot()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down bot...")
    except Exception as e:
        logger.error(f"Error running bot: {e}")
        logger.error(traceback.format_exc())

    logger.info("Bot has been shut down.")

if __name__ == "__main__":
    run_all_bots()

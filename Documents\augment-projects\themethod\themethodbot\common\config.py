import logging

# Global logging configuration
DEBUG_MODE = False  # Set to True to enable debug logging

def configure_logging(debug_mode: bool = DEBUG_MODE):
    """Configure logging based on debug mode"""
    global DEBUG_MODE
    DEBUG_MODE = debug_mode
    
    if DEBUG_MODE:
        log_level = logging.DEBUG
        format_str = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    else:
        log_level = logging.INFO
        format_str = '%(asctime)s - %(levelname)s - %(message)s'

    # Remove all handlers
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format=format_str,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('themethodbot.log', encoding='utf-8')
        ]
    )

    # Return the logger for convenience
    return logging.getLogger('themethodbot')

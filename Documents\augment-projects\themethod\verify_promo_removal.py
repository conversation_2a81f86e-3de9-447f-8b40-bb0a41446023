#!/usr/bin/env python3
"""
Simple verification script to confirm promo checking has been removed.
"""

import os

def check_file_modifications():
    """Check that key files have been modified correctly"""
    
    print("🔍 Verifying promo removal modifications...")
    print("=" * 50)
    
    checks = [
        {
            'file': 'common/check_group_order.py',
            'expected': ['is_promo = True', 'Promo checking removed'],
            'not_expected': ['await check_store_promo(store_url)']
        },
        {
            'file': 'common/bot.py', 
            'expected': ['is_promo = True', 'Promo checking removed'],
            'not_expected': ['await check_store_promo(store_url)']
        },
        {
            'file': 'themethodbot/themethodbot.py',
            'expected': ['Promo checking removed'],
            'not_expected': ['if \'is_promo\' in result and not result[\'is_promo\']']
        },
        {
            'file': 'themethodbot/embed_templates.py',
            'expected': ['Store is in promo!', 'Promo checking removed'],
            'not_expected': ['result.get(\'is_promo\', True)']
        }
    ]
    
    all_passed = True
    
    for check in checks:
        file_path = check['file']
        print(f"\n📁 Checking {file_path}...")
        
        if not os.path.exists(file_path):
            print(f"❌ File not found: {file_path}")
            all_passed = False
            continue
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check expected content
        for expected in check['expected']:
            if expected in content:
                print(f"✅ Found expected: '{expected}'")
            else:
                print(f"❌ Missing expected: '{expected}'")
                all_passed = False
        
        # Check that unwanted content is removed
        for not_expected in check['not_expected']:
            if not_expected not in content:
                print(f"✅ Correctly removed: '{not_expected}'")
            else:
                print(f"❌ Still contains: '{not_expected}'")
                all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All verification checks passed!")
        print("✅ Promo checking functionality has been successfully removed.")
        print("✅ Order summary embeds will now always be displayed.")
    else:
        print("⚠️ Some verification checks failed.")
        print("Please review the results above.")
    
    return all_passed

def main():
    """Main verification function"""
    print("🚀 Starting promo removal verification...")
    success = check_file_modifications()
    
    if success:
        print("\n📋 Summary of changes made:")
        print("• Removed conditional logic that prevented summary embeds when stores weren't in promo")
        print("• Modified process_group_order functions to always set is_promo = True")
        print("• Updated embed templates to always show 'Store is in promo!' status")
        print("• Bypassed all check_store_promo() function calls")
        print("• Maintained all other bot functionality (fee calculations, order processing, etc.)")
        
        print("\n🎯 Result:")
        print("Order summary embeds will now be generated and displayed for ALL stores,")
        print("regardless of their actual promotional status.")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test script to verify the persistent embed fix for order status tracking.
This script simulates the tracking data flow to ensure the persistent embed logic works correctly.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock Discord objects for testing
class MockChannel:
    def __init__(self, channel_id=12345):
        self.id = channel_id
        self.messages = {}
        self.sent_messages = []
    
    async def send(self, content=None, embed=None):
        message_id = len(self.messages) + 1
        message = MockMessage(message_id, content, embed)
        self.messages[message_id] = message
        self.sent_messages.append(message)
        print(f"📤 SENT new message (ID: {message_id})")
        if embed:
            print(f"   Title: {embed.title}")
            print(f"   Description: {embed.description}")
        return message
    
    async def fetch_message(self, message_id):
        if message_id in self.messages:
            return self.messages[message_id]
        raise Exception("Message not found")

class MockMessage:
    def __init__(self, message_id, content=None, embed=None):
        self.id = message_id
        self.content = content
        self.embed = embed
        self.edit_count = 0
    
    async def edit(self, content=None, embed=None):
        self.edit_count += 1
        if embed:
            self.embed = embed
        if content:
            self.content = content
        print(f"✏️  EDITED message (ID: {self.id}, Edit #{self.edit_count})")
        if embed:
            print(f"   Title: {embed.title}")
            print(f"   Description: {embed.description}")

class MockEmbed:
    def __init__(self, title="", description="", color=None):
        self.title = title
        self.description = description
        self.color = color
        self.fields = []
        self.thumbnail = None
        self.timestamp = None
    
    def add_field(self, name, value, inline=False):
        self.fields.append({"name": name, "value": value, "inline": inline})
    
    def set_thumbnail(self, url):
        self.thumbnail = url
    
    def set_timestamp(self, timestamp):
        self.timestamp = timestamp

# Mock discord module
class MockDiscord:
    class Embed:
        def __init__(self, title="", description="", color=None):
            return MockEmbed(title, description, color)
    
    class Color:
        @staticmethod
        def from_rgb(r, g, b):
            return f"rgb({r},{g},{b})"
        
        @staticmethod
        def gold():
            return "gold"

# Define the functions locally for testing (copied from common/bot.py)
def add_status_to_history(status_history, status_text):
    """Add a status update to the history with Discord timestamp."""
    import time
    current_time = int(time.time())
    timestamp_str = f"<t:{current_time}:t>"

    # Add new status with timestamp
    status_history.append(f"{timestamp_str} - {status_text}")

    # Keep only the last 10 status updates to prevent embed from getting too long
    if len(status_history) > 10:
        status_history = status_history[-10:]

    return status_history

def format_status_history(status_history):
    """Format the status history for display in embed."""
    if not status_history:
        return "No status updates yet"

    # Join all status updates with newlines
    return '\n'.join(status_history)

def get_status_display_text(order_status):
    """Convert order status code to user-friendly display text."""
    status_map = {
        "OrderPlaced": "Order has been placed with the Store 📝",
        "Preparing": "Store is preparing your order 👨‍🍳",
        "ReadyForPickup": "Order is ready for pickup 📦",
        "PickedUp": "Driver has picked up your order 🚗",
        "EnrouteToDropoff": "Driver is on the way to you 🚚",
        "EnrouteToEater": "Driver is on the way to you 🚚",
        "EnrouteToRestaurant": "Driver is heading to the Store 🚗",
        "AtRestaurant": "Driver has arrived at the Store 🏪",
        "FindingNewCourier": "Finding a new driver 🚗",
        "TimeToMeetCourier": "The driver is about to drop off your order! 🚨",
        "ArrivedAtDropoff": "The driver is about to drop off your order! 🚨"
    }

    return status_map.get(order_status, f"Current Status: {order_status}")

async def test_persistent_embed_logic():
    """Test the persistent embed logic with simulated tracking data."""
    print("🧪 Testing Persistent Embed Logic")
    print("=" * 50)
    
    # Create mock objects
    channel = MockChannel()
    
    # Simulate tracking data structure
    tracking_data = {
        'channel_id': channel.id,
        'start_time': 1234567890,
        'last_status': None,
        'order_link': 'https://www.ubereats.com/orders/test-order-123',
        'status_embed_message_id': None,
        'status_history': [],
        'delivery_ping_sent': False
    }
    
    # Test status updates
    status_updates = [
        "Preparing",
        "ReadyForPickup", 
        "PickedUp",
        "EnrouteToDropoff"
    ]
    
    print("📋 Simulating status updates...")
    
    for i, status in enumerate(status_updates):
        print(f"\n🔄 Status Update #{i+1}: {status}")
        
        # Add status to history (this is what the fixed function should do)
        status_display = get_status_display_text(status)
        tracking_data['status_history'] = add_status_to_history(
            tracking_data.get('status_history', []),
            status_display
        )
        
        # Create embed (simulating the embed creation in track_order_status)
        embed = MockEmbed(
            title="🚗 Order Status Update",
            description=f"**{status_display}**\n\nYour order is being processed."
        )
        
        # Add status history to embed
        status_history_text = format_status_history(tracking_data.get('status_history', []))
        embed.add_field(
            name="📋 Status History",
            value=status_history_text,
            inline=False
        )
        
        # Simulate persistent embed logic
        if tracking_data.get('status_embed_message_id'):
            # Should edit existing message
            try:
                message = await channel.fetch_message(tracking_data['status_embed_message_id'])
                await message.edit(embed=embed)
                print(f"   ✅ Edited existing embed (Message ID: {tracking_data['status_embed_message_id']})")
            except Exception as e:
                print(f"   ❌ Failed to edit: {e}")
                # Fallback to new message
                new_message = await channel.send(embed=embed)
                tracking_data['status_embed_message_id'] = new_message.id
                print(f"   📤 Sent new message as fallback (ID: {new_message.id})")
        else:
            # Should create new message and store ID
            new_message = await channel.send(embed=embed)
            tracking_data['status_embed_message_id'] = new_message.id
            print(f"   📤 Created first embed (Message ID: {new_message.id})")
    
    # Verify results
    print(f"\n📊 Test Results:")
    print(f"   Total messages sent: {len(channel.sent_messages)}")
    print(f"   Expected: 1 (persistent embed)")
    
    if len(channel.sent_messages) == 1:
        message = channel.sent_messages[0]
        print(f"   ✅ SUCCESS: Only 1 message sent (ID: {message.id})")
        print(f"   ✅ Message was edited {message.edit_count} times")
        print(f"   ✅ Final status history has {len(tracking_data['status_history'])} entries")
        
        # Show final embed content
        if message.embed and message.embed.fields:
            for field in message.embed.fields:
                if field['name'] == "📋 Status History":
                    print(f"   📋 Final Status History:")
                    for line in field['value'].split('\n'):
                        if line.strip():
                            print(f"      {line}")
    else:
        print(f"   ❌ FAILURE: {len(channel.sent_messages)} messages sent instead of 1")
        for i, msg in enumerate(channel.sent_messages):
            print(f"      Message {i+1}: ID {msg.id}, Edits: {msg.edit_count}")

async def main():
    """Run the test."""
    await test_persistent_embed_logic()
    print(f"\n🎯 Test completed!")

if __name__ == "__main__":
    asyncio.run(main())

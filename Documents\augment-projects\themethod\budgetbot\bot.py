#!/usr/bin/env python3
"""
Generic Discord Bot - Minimal bot for order processing
Features:
- Automatic group order link detection and processing
- Basic order tracking functionality
"""

import discord
from discord.ext import commands
from discord import app_commands
import logging
import os
from dotenv import load_dotenv
import re
import asyncio
import traceback
import time
import aiohttp
import json
import datetime
from typing import Dict, Optional, Any, List

import sys
import os

# Import essential functions only
from common.bot import (
    extract_group_link,
    fetch_order_details,
    track_order_status
)
from common.check_group_order import process_group_order

# Import essential embed templates only
from budgetbot.embed_templates import (
    create_locked_order_embed,
    create_order_summary_embed,
    create_error_embed,
    create_processing_embed
)

# Import fee calculator
from budgetbot.fee_calculator import calculate_fees_without_delivery

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('bot.log', encoding='utf-8')
    ]
)

# Create a logger for this module
logger = logging.getLogger('generic_bot')

# Get environment variables
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN")
DISCORD_GUILD_ID = int(os.getenv("DISCORD_GUILD_ID"))
COMMANDS_GLOBAL = False

# Set up bot
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.message_content = True
intents.dm_messages = True

# Create the bot
bot = commands.Bot(
    command_prefix="!bot",
    intents=intents,
    dm_permission=True,
    case_insensitive=True
)

GUILD = discord.Object(id=DISCORD_GUILD_ID)

# Global session for HTTP requests
_http_session: Optional[aiohttp.ClientSession] = None

# Active tracking information (minimal)
active_tracking: Dict[str, Dict[str, Any]] = {}

# Essential helper functions
async def get_session() -> aiohttp.ClientSession:
    """Get or create the global HTTP session."""
    global _http_session
    if _http_session is None or _http_session.closed:
        _http_session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=20, ttl_dns_cache=300)
        )
    return _http_session

# Bot events
@bot.event
async def setup_hook():
    """Set up tasks before the bot starts."""
    # Initialize the HTTP session
    await get_session()
    
    # Record start time for uptime tracking
    bot.launch_time = time.time()
    
    # Initialize tracking tasks list
    bot.tracking_tasks = []
    
    logger.info("Bot setup completed")

@bot.event
async def on_ready():
    """Called when the bot is ready."""
    logger.info(f"Bot logged in as {bot.user}")
    logger.info(f"Command mode: {'Global' if COMMANDS_GLOBAL else 'Guild-only'}")

    try:
        if COMMANDS_GLOBAL:
            synced = await bot.tree.sync()
            logger.info(f"Synced {len(synced)} command(s) globally")
        else:
            bot.tree.copy_global_to(guild=GUILD)
            synced = await bot.tree.sync(guild=GUILD)
            logger.info(f"Synced {len(synced)} command(s) to guild")
    except Exception as e:
        logger.error(f"Failed to sync commands: {e}")
        logger.error(traceback.format_exc())

@bot.event
async def on_message(message):
    """Handle automatic group order link detection and processing."""
    # Process commands first
    await bot.process_commands(message)

    # Skip messages from the bot itself
    if message.author == bot.user:
        return

    try:
        # Extract group link
        group_link = await extract_group_link(message)
        if not group_link:
            return

        # Normalize the link to ensure consistent matching
        if '?' in group_link:
            group_link = group_link.split('?')[0]
        if '#' in group_link:
            group_link = group_link.split('#')[0]

        # Ensure the link ends with /join for consistency
        if not group_link.endswith('/join'):
            if group_link.endswith('/'):
                group_link = group_link + 'join'
            else:
                group_link = group_link + '/join'

        logger.info(f"Group order link detected: {group_link}")

        # Send a processing message
        processing_message = await message.channel.send(embed=create_processing_embed())

        # Process group order
        result = await process_group_order(group_link)

        # Add the group link to the result dictionary
        if isinstance(result, dict):
            result['group_link'] = group_link

        # Handle locked/error cases
        if isinstance(result, dict) and 'error' in result:
            if result['error'].get('type') == 'LOCKED_ORDER':
                await processing_message.delete()
                await message.channel.send(embed=create_locked_order_embed())
                return

        if not result:
            await processing_message.delete()
            await message.channel.send(embed=create_locked_order_embed())
            return

        # Process cart items
        cart_items = []
        calculated_subtotal = 0

        if 'cart_items' in result:
            for item in result['cart_items']:
                price = item.get('price', 0)
                quantity = item.get('quantity', 1)
                title = item.get('title', 'Unknown Item')
                cart_items.append(f"{title} x{quantity} (${price/100:.2f})")
                calculated_subtotal += (price * quantity) / 100

        # Calculate fees (simplified)
        if 'fees' in result and result['fees']:
            fees_data = result['fees']
            if 'subtotal' in fees_data and fees_data['subtotal'] > 0:
                subtotal = float(fees_data['subtotal'])
            else:
                subtotal = calculated_subtotal
            fee_calculations = calculate_fees_without_delivery(fees_data, subtotal, False)
        else:
            subtotal = calculated_subtotal
            fee_calculations = calculate_fees_without_delivery({}, subtotal, False)

        # Delete the processing message
        await processing_message.delete()

        # Create and send order summary
        summary_embed = create_order_summary_embed(result, cart_items, fee_calculations)
        await message.channel.send(embed=summary_embed)

        # Send a simple success message
        success_embed = discord.Embed(
            title="✅ Order Processed",
            description="Your order has been processed successfully!",
            color=discord.Color.green()
        )
        success_embed.set_footer(text="Order Processing Bot")
        await message.channel.send(embed=success_embed)

    except Exception as e:
        logger.error(f"Error processing group order link: {e}")
        logger.error(traceback.format_exc())
        
        # Clean up processing message if it exists
        try:
            if 'processing_message' in locals():
                await processing_message.delete()
        except:
            pass

        # Send a nicely formatted error message
        await message.channel.send(embed=create_error_embed(str(e)))

# Commands section - Only /ordersuccess command

@bot.tree.command(
    name="ordersuccess",
    description="Track a successful order using order link",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(orderlink="The order link to track")
async def ordersuccess_slash(interaction: discord.Interaction, orderlink: str):
    """Simplified order success tracking command."""
    try:
        logger.info(f"/ordersuccess triggered by {interaction.user} with order link: {orderlink}")
        await interaction.response.defer()

        # Extract order ID and fetch details
        order_id = re.search(r"orders/([a-f0-9-]+)", orderlink)
        if order_id:
            order_id = order_id.group(1)

            # Get the HTTP session
            session = await get_session()

            # Fetch order details
            order_details = await fetch_order_details(order_id, session)

            if order_details:
                # Create simple success embed
                order_embed = discord.Embed(
                    title="🎉 Order Success",
                    description="Your order has been successfully placed and is being tracked!",
                    color=discord.Color.blue()
                )

                # Add basic order information
                order_embed.add_field(name="🏪 Restaurant", value=order_details.get('store', 'Unknown'), inline=True)
                order_embed.add_field(name="⏰ ETA", value=order_details['eta'], inline=True)
                order_embed.add_field(name="👤 Customer", value=order_details.get('customer', interaction.user.display_name), inline=True)
                order_embed.add_field(name="📍 Address", value=f"```{order_details['address']}```", inline=False)
                order_embed.add_field(name="🔗 Order Link", value=f"[View Order]({orderlink})", inline=False)

                order_embed.set_footer(text="Order Tracking Bot")
                order_embed.timestamp = datetime.datetime.now()

                # Send the embed
                await interaction.followup.send(embed=order_embed)

                # Store tracking information (simplified)
                active_tracking[order_id] = {
                    'channel_id': interaction.channel.id,
                    'start_time': time.time(),
                    'order_link': orderlink
                }

                # Start tracking in a background task (simplified)
                try:
                    tracking_task = asyncio.create_task(
                        track_order_status(order_id, interaction.channel, session, bot_name='generic_bot')
                    )
                    if not hasattr(bot, 'tracking_tasks'):
                        bot.tracking_tasks = []
                    bot.tracking_tasks.append(tracking_task)
                    logger.info(f"Started tracking for order {order_id}")
                except Exception as e:
                    logger.error(f"Error starting tracking: {e}")
            else:
                await interaction.followup.send("❌ Failed to fetch order details.", ephemeral=True)
        else:
            await interaction.followup.send("❌ Invalid order link format.", ephemeral=True)

        logger.info("Order success tracking completed.")

    except Exception as e:
        logger.error(f"Error in ordersuccess command: {e}")
        logger.error(traceback.format_exc())
        try:
            if interaction.response.is_done():
                await interaction.followup.send(f"❌ An error occurred: {e}", ephemeral=True)
            else:
                await interaction.response.send_message(f"❌ An error occurred: {e}", ephemeral=True)
        except Exception:
            pass

# Run the bot
def run_bot():
    """Run the bot."""
    try:
        bot.run(DISCORD_BOT_TOKEN, log_handler=None)
    except KeyboardInterrupt:
        logger.info("Bot stopped by keyboard interrupt")
    except Exception as e:
        logger.error(f"Error running bot: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    run_bot()

#!/usr/bin/env python3
"""
Serial Key Generator for TheMethod Credits System
Generates 1,000 unique serial keys for each credit denomination.
"""

import secrets
import string
import os
from typing import Set, List

def generate_secure_key(length: int = 7) -> str:
    """Generate a secure random key using uppercase letters and numbers."""
    # Use uppercase letters and numbers for professional appearance
    alphabet = string.ascii_uppercase + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def generate_unique_keys(count: int, length: int = 7) -> List[str]:
    """Generate a specified number of unique keys."""
    keys: Set[str] = set()
    
    # Generate keys until we have the required count
    while len(keys) < count:
        key = generate_secure_key(length)
        keys.add(key)
    
    return sorted(list(keys))

def save_keys_to_file(keys: List[str], filename: str) -> None:
    """Save keys to a text file, one key per line."""
    with open(filename, 'w') as f:
        for key in keys:
            f.write(f"{key}\n")
    print(f"Generated {len(keys)} keys and saved to {filename}")

def main():
    """Generate serial keys for all credit denominations."""
    print("🔑 Generating Serial Keys for TheMethod Credits System")
    print("=" * 60)
    
    # Define credit denominations and their corresponding filenames
    denominations = {
        3: "3keys.txt",
        5: "5keys.txt", 
        10: "10keys.txt",
        20: "20keys.txt"
    }
    
    # Generate 1,000 keys for each denomination
    keys_per_denomination = 1000
    key_length = 7
    
    for amount, filename in denominations.items():
        print(f"Generating {keys_per_denomination} keys for ${amount} credits...")
        
        # Generate unique keys
        keys = generate_unique_keys(keys_per_denomination, key_length)
        
        # Save to file
        filepath = os.path.join(os.path.dirname(__file__), filename)
        save_keys_to_file(keys, filepath)
        
        print(f"✅ ${amount} credit keys saved to {filename}")
        print(f"   Sample keys: {', '.join(keys[:3])}...")
        print()
    
    print("🎉 All serial keys generated successfully!")
    print("\nFiles created:")
    for amount, filename in denominations.items():
        filepath = os.path.join(os.path.dirname(__file__), filename)
        if os.path.exists(filepath):
            with open(filepath, 'r') as f:
                line_count = sum(1 for _ in f)
            print(f"  - {filename}: {line_count} keys (${amount} each)")
    
    print("\n⚠️  SECURITY NOTICE:")
    print("   - Keep these key files secure and private")
    print("   - Do not share keys publicly")
    print("   - Back up these files safely")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test script to verify the fixed fee implementation in themethodbot.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from themethodbot.fee_calculator import calculate_fees_without_delivery
from common.bot import calculate_fees

def test_fixed_fee_implementation():
    """Test that the fixed fee is properly included in calculations."""
    print("🧪 Testing Fixed Fee Implementation")
    print("=" * 50)
    
    # Test data
    test_subtotal = 25.00
    test_fees_data = {
        'service_fee': 2.50,
        'ca_driver_benefit': 0.00,
        'taxes': 2.00,
        'uber_one_discount': 0.00,
        'delivery_fee': 1.99
    }
    
    print(f"📊 Test Input:")
    print(f"   Subtotal: ${test_subtotal:.2f}")
    print(f"   Service Fee: ${test_fees_data['service_fee']:.2f}")
    print(f"   Taxes: ${test_fees_data['taxes']:.2f}")
    print(f"   Delivery Fee: ${test_fees_data['delivery_fee']:.2f}")
    print()
    
    # Test USD calculations
    print("🇺🇸 Testing USD Calculations:")
    usd_result = calculate_fees_without_delivery(test_fees_data, test_subtotal, is_cad=False)
    
    print(f"   Fixed Fee: ${usd_result.get('fixed_fee', 0):.2f}")
    print(f"   Total Fees: ${usd_result.get('total_fees', 0):.2f}")
    print(f"   Final Fees: ${usd_result.get('final_fees', 0):.2f}")
    print(f"   Final Total: ${usd_result.get('final_total', 0):.2f}")
    print()
    
    # Test CAD calculations
    print("🇨🇦 Testing CAD Calculations:")
    cad_result = calculate_fees_without_delivery(test_fees_data, test_subtotal, is_cad=True)
    
    print(f"   Fixed Fee: ${cad_result.get('fixed_fee', 0):.2f}")
    print(f"   Total Fees: ${cad_result.get('total_fees', 0):.2f}")
    print(f"   Final Fees: ${cad_result.get('final_fees', 0):.2f}")
    print(f"   Final Total: ${cad_result.get('final_total', 0):.2f}")
    print()
    
    # Test full fee calculation with delivery
    print("🚚 Testing Full Fee Calculation (with delivery):")
    full_result = calculate_fees(test_fees_data, test_subtotal, is_cad=False)
    
    print(f"   Fixed Fee: ${full_result.get('fixed_fee', 0):.2f}")
    print(f"   Total Fees: ${full_result.get('total_fees', 0):.2f}")
    print(f"   Final Fees: ${full_result.get('final_fees', 0):.2f}")
    print(f"   Final Total: ${full_result.get('final_total', 0):.2f}")
    print()
    
    # Verify expected values
    print("✅ Verification:")
    expected_usd_fixed_fee = 7.50
    expected_cad_fixed_fee = 9.75
    
    if usd_result.get('fixed_fee') == expected_usd_fixed_fee:
        print(f"   ✓ USD Fixed Fee: ${expected_usd_fixed_fee:.2f} (PASS)")
    else:
        print(f"   ✗ USD Fixed Fee: Expected ${expected_usd_fixed_fee:.2f}, got ${usd_result.get('fixed_fee', 0):.2f} (FAIL)")
    
    if cad_result.get('fixed_fee') == expected_cad_fixed_fee:
        print(f"   ✓ CAD Fixed Fee: ${expected_cad_fixed_fee:.2f} (PASS)")
    else:
        print(f"   ✗ CAD Fixed Fee: Expected ${expected_cad_fixed_fee:.2f}, got ${cad_result.get('fixed_fee', 0):.2f} (FAIL)")
    
    if full_result.get('fixed_fee') == expected_usd_fixed_fee:
        print(f"   ✓ Full Calculation Fixed Fee: ${expected_usd_fixed_fee:.2f} (PASS)")
    else:
        print(f"   ✗ Full Calculation Fixed Fee: Expected ${expected_usd_fixed_fee:.2f}, got ${full_result.get('fixed_fee', 0):.2f} (FAIL)")
    
    print()
    print("🎯 Test Summary:")
    print("   The fixed fee implementation adds $7.50 for USD and $9.75 for CAD")
    print("   to all fee calculations, replacing the previous dynamic-only system.")
    print("   This implements the new pricing model: 'Subtotal - $20 + tax + $7.50 fee'")

if __name__ == "__main__":
    test_fixed_fee_implementation()

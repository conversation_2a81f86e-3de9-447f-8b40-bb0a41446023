"""
BudgetBot Embed Templates
Simplified embed templates for BudgetBot with budget-focused styling and branding.
Uses the same simplified format as themethodbot but with budget-specific colors and messaging.
"""

import discord
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger('budgetbot.embed_templates')

# BudgetBot specific colors
BUDGETBOT_PRIMARY_COLOR = discord.Color.from_rgb(255, 165, 0)  # Orange - budget-friendly color
BUDGETBOT_SUCCESS_COLOR = discord.Color.green()
BUDGETBOT_ERROR_COLOR = discord.Color.red()
BUDGETBOT_WARNING_COLOR = discord.Color.yellow()

def create_budgetbot_order_summary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a simplified BudgetBot order summary embed with essential information only."""
    embed = discord.Embed(
        title="🍔 BudgetBot Order Summary",
        color=BUDGETBOT_PRIMARY_COLOR
    )

    # Add group order link if available
    group_link = result.get('group_link', 'Not available')
    embed.description = f"**🔗 [Group Order Link]({group_link})**\n\n"

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="📍 Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="🏪 Restaurant",
            value=f"[View on Uber Eats]({store_url})",
            inline=False
        )

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Add subtotal only (simplified pricing)
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"
    subtotal = fee_calculations.get('subtotal', 0)
    
    embed.add_field(
        name="💰 Subtotal",
        value=f"`{currency}{subtotal:.2f}`",
        inline=False
    )

    # Set BudgetBot thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")

    # Add BudgetBot footer
    embed.set_footer(text="BudgetBot | Budget-Friendly Order Summary")

    return embed

def create_budgetbot_latestsummary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a simplified BudgetBot embed specifically for the /latestsummary command."""
    embed = discord.Embed(
        title="🍔 BudgetBot Order Summary",
        color=discord.Color.from_rgb(255, 140, 0)  # Darker orange for latestsummary
    )

    # Add group order link if available
    group_link = result.get('group_link', 'Not available')
    if group_link != 'Not available':
        embed.description = f"🔗 **[Group Order Link]({group_link})**\n\n"
    else:
        embed.description = ""

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="📍 Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="🏪 Restaurant",
            value=f"[View on Uber Eats]({store_url})",
            inline=False
        )

    # Add cart items with budget-friendly formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = ""
        for item in items_to_show:
            items_str += f"💸 *{item}*\n"

        if remaining_items > 0:
            items_str += f"💸 *... and {remaining_items} more items*"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Add subtotal only (simplified pricing)
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"
    subtotal = fee_calculations.get('subtotal', 0)
    
    embed.add_field(
        name="💰 Subtotal",
        value=f"`{currency}{subtotal:.2f}`",
        inline=False
    )

    # Set BudgetBot thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360135017019347135/food.gif?ex=67fa03f2&is=67f8b272&hm=2722a45f152c6b1f0f7f52e2cf1e890bca3d70c6934d6349646ae7e43f76b375&=")

    # BudgetBot footer
    embed.set_footer(text="BudgetBot | Budget-Friendly Order Summary")

    return embed

def create_budgetbot_error_embed(message: str) -> discord.Embed:
    """Create a BudgetBot-styled error embed."""
    embed = discord.Embed(
        title="❌ BudgetBot Error",
        description=message,
        color=BUDGETBOT_ERROR_COLOR
    )
    embed.set_footer(text="BudgetBot | Error")
    return embed

def create_budgetbot_processing_embed() -> discord.Embed:
    """Create a BudgetBot-styled processing embed."""
    embed = discord.Embed(
        title="⏳ BudgetBot Processing",
        description="Processing your budget-friendly order...",
        color=BUDGETBOT_WARNING_COLOR
    )
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1360139708234993674/loading.gif?ex=67fa0850&is=67f8b6d0&hm=a4286ff0bd61b871ec51cb03ce8a589cd24d93f101bb10fa414567816d9c4a57&=")
    embed.set_footer(text="BudgetBot | Processing")
    return embed

def check_order_limits(subtotal: float, is_cad: bool = False) -> Optional[discord.Embed]:
    """BudgetBot version of order limits checking with budget-friendly messaging."""
    currency = "CA$" if is_cad else "$"
    min_order = 30.0 if is_cad else 20.0
    max_order = 35.0 if is_cad else 35.0

    if subtotal < min_order:
        embed = discord.Embed(
            title="💸 Order Too Small for Budget Service",
            description=f"Your order subtotal is **{currency}{subtotal:.2f}**",
            color=BUDGETBOT_ERROR_COLOR
        )
        embed.add_field(
            name="Minimum Required",
            value=f"{currency}{min_order:.2f}",
            inline=True
        )
        embed.add_field(
            name="Budget Tip",
            value="Add more items to reach our budget-friendly minimum!",
            inline=False
        )
        embed.set_footer(text="BudgetBot | Order Validation")
        return embed

    if subtotal > max_order:
        embed = discord.Embed(
            title="💰 Order Too Large for Budget Service",
            description=f"Your order subtotal is **{currency}{subtotal:.2f}**",
            color=BUDGETBOT_ERROR_COLOR
        )
        embed.add_field(
            name="Maximum Allowed",
            value=f"{currency}{max_order:.2f}",
            inline=True
        )
        embed.add_field(
            name="Budget Tip",
            value="Consider splitting into multiple budget-friendly orders!",
            inline=False
        )
        embed.set_footer(text="BudgetBot | Order Validation")
        return embed

    return None

async def check_order_limits(interaction: discord.Interaction, grouporderlink: str):
    """BudgetBot version of the check command with simplified embed format."""
    try:
        await interaction.response.defer(ephemeral=True)
        
        # Show processing embed
        processing_embed = create_budgetbot_processing_embed()
        await interaction.followup.send(embed=processing_embed, ephemeral=True)
        
        # Process the group order
        from common.check_group_order import process_group_order
        result = await process_group_order(grouporderlink)
        
        if not result:
            error_embed = create_budgetbot_error_embed("Could not process the group order link.")
            await interaction.edit_original_response(embed=error_embed)
            return
        
        # Extract cart items
        cart_items = result.get('cart_items', [])
        
        # Calculate fees (simplified for budget version)
        fees_data = {
            'service_fee': 0.0,
            'delivery_fee': 0.0,
            'ca_driver_benefit': 0.0,
            'taxes': 0.0,
            'uber_one_discount': 0.0
        }
        
        from common.bot import calculate_fees
        subtotal = result.get('subtotal', 0.0)
        fee_calculations = calculate_fees(fees_data, subtotal, is_cad=False)
        
        # Check order limits
        limit_check = check_order_limits(subtotal, is_cad=False)
        if limit_check:
            await interaction.edit_original_response(embed=limit_check)
            return
        
        # Create budget-friendly summary embed
        summary_embed = create_budgetbot_order_summary_embed(result, cart_items, fee_calculations)
        await interaction.edit_original_response(embed=summary_embed)
        
        logger.info(f"BudgetBot check command executed by {interaction.user}")
        
    except Exception as e:
        logger.error(f"Error in BudgetBot check command: {e}")
        error_embed = create_budgetbot_error_embed("An error occurred while checking the order.")
        await interaction.edit_original_response(embed=error_embed)

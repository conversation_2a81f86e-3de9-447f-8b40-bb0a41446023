import discord
import logging
from typing import List, Dict, Any, Optional

logger = logging.getLogger('generic_bot.embeds')

def create_locked_order_embed():
    """Create a generic embed for locked/canceled orders."""
    embed = discord.Embed(
        title="🔒 Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.red()
    )

    embed.add_field(
        name="Step 1",
        value="Go back to your cart",
        inline=False
    )

    embed.add_field(
        name="Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    return embed



def create_order_summary_embed(result: Dict[str, Any], cart_items: List[str], fee_calculations: Dict[str, Any]) -> discord.Embed:
    """Create a generic order summary embed."""
    embed = discord.Embed(
        title="🍔 Order Summary",
        color=discord.Color.blue()
    )

    # Add group order link if available
    group_link = result.get('group_link', 'Not available')
    embed.description = f"**[Group Order Link]({group_link})**\n\n"

    # Location
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="📍 Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="🏪 Restaurant",
            value=f"[View Restaurant]({store_url})",
            inline=False
        )

    # Add cart items
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # Add subtotal
    currency = "CA$" if fee_calculations.get('is_cad', False) else "$"
    subtotal = fee_calculations.get('subtotal', 0)

    embed.add_field(
        name="💰 Subtotal",
        value=f"`{currency}{subtotal:.2f}`",
        inline=False
    )

    embed.set_footer(text="Order Processing Bot")

    return embed



def create_error_embed(error_message: str) -> discord.Embed:
    """Create a generic error embed."""
    embed = discord.Embed(
        title="❌ Error Processing Order",
        description=f"An error occurred while processing your order:\n\n```{error_message}```",
        color=discord.Color.red()
    )

    # Add troubleshooting steps
    embed.add_field(
        name="🔧 Troubleshooting Steps",
        value="1. Make sure your group order is unlocked\n" +
              "2. Try creating a new group order\n" +
              "3. Check that the link is valid",
        inline=False
    )

    embed.set_footer(text="If the issue persists, please try again later")

    return embed

def create_processing_embed() -> discord.Embed:
    """Create a generic processing embed."""
    embed = discord.Embed(
        title="🔄 Processing Order",
        description="Analyzing your group order. This may take a few seconds...",
        color=discord.Color.blurple()
    )

    return embed



#!/usr/bin/env python3
"""
Test script to verify that main.py argument parser correctly handles both bot options.
"""

import subprocess
import sys
import os

def test_argument_parser():
    """Test that main.py argument parser works correctly for both bots."""
    print("🧪 Testing main.py Argument Parser")
    print("=" * 50)
    
    # Test 1: Help command shows both bots
    print("\n📝 Test 1: Help Command Shows Both Bots")
    try:
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            help_output = result.stdout
            if "themethodbot,budgetbot" in help_output:
                print("   ✅ Both bots listed in choices")
            else:
                print("   ❌ Both bots not found in choices")
                return False
                
            if "themethodbot (main service)" in help_output and "budgetbot (budget-friendly service)" in help_output:
                print("   ✅ Descriptive help text present")
            else:
                print("   ❌ Descriptive help text missing")
                print(f"   Help output: {help_output}")
                return False
        else:
            print(f"   ❌ Help command failed with return code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing help command: {e}")
        return False
    
    # Test 2: Invalid bot name is rejected
    print("\n📝 Test 2: Invalid Bot Name Rejection")
    try:
        result = subprocess.run([sys.executable, "main.py", "invalidbot"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0:
            error_output = result.stderr
            if "invalid choice: 'invalidbot'" in error_output and "choose from 'themethodbot', 'budgetbot'" in error_output:
                print("   ✅ Invalid bot name properly rejected")
            else:
                print("   ❌ Invalid bot name not properly rejected")
                return False
        else:
            print("   ❌ Invalid bot name was accepted (should be rejected)")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing invalid bot name: {e}")
        return False
    
    # Test 3: Valid bot names are accepted (themethodbot)
    print("\n📝 Test 3: Valid Bot Names Accepted")
    try:
        # Test themethodbot (just check that it starts importing, then kill quickly)
        result = subprocess.run([sys.executable, "-c", 
                               "import sys; sys.argv = ['main.py', 'themethodbot']; "
                               "import argparse; "
                               "parser = argparse.ArgumentParser(); "
                               "parser.add_argument('bot', choices=['themethodbot', 'budgetbot']); "
                               "args = parser.parse_args(); "
                               "print(f'✅ themethodbot accepted: {args.bot}')"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "✅ themethodbot accepted: themethodbot" in result.stdout:
            print("   ✅ themethodbot argument accepted")
        else:
            print("   ❌ themethodbot argument not accepted")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing themethodbot argument: {e}")
        return False
    
    # Test 4: budgetbot argument is accepted
    try:
        result = subprocess.run([sys.executable, "-c", 
                               "import sys; sys.argv = ['main.py', 'budgetbot']; "
                               "import argparse; "
                               "parser = argparse.ArgumentParser(); "
                               "parser.add_argument('bot', choices=['themethodbot', 'budgetbot']); "
                               "args = parser.parse_args(); "
                               "print(f'✅ budgetbot accepted: {args.bot}')"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "✅ budgetbot accepted: budgetbot" in result.stdout:
            print("   ✅ budgetbot argument accepted")
        else:
            print("   ❌ budgetbot argument not accepted")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing budgetbot argument: {e}")
        return False
    
    # Test 5: Verify import functionality works
    print("\n📝 Test 5: Import Functionality")
    try:
        # Test that the run_bot function can handle both bot names
        result = subprocess.run([sys.executable, "-c", 
                               "import sys; sys.path.append('.'); "
                               "from main import run_bot; "
                               "import importlib.util; "
                               "# Test themethodbot import "
                               "spec1 = importlib.util.find_spec('themethodbot.themethodbot'); "
                               "print('✅ themethodbot module found' if spec1 else '❌ themethodbot module not found'); "
                               "# Test budgetbot import "
                               "spec2 = importlib.util.find_spec('budgetbot.budgetbot'); "
                               "print('✅ budgetbot module found' if spec2 else '❌ budgetbot module not found')"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            output = result.stdout
            if "✅ themethodbot module found" in output and "✅ budgetbot module found" in output:
                print("   ✅ Both bot modules can be imported")
            else:
                print("   ❌ One or both bot modules cannot be imported")
                print(f"   Output: {output}")
                return False
        else:
            print(f"   ❌ Import test failed with return code {result.returncode}")
            print(f"   Error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error testing import functionality: {e}")
        return False
    
    print("\n✅ SUCCESS: All argument parser tests passed!")
    return True

def main():
    """Run the argument parser tests."""
    print("🔧 MAIN.PY ARGUMENT PARSER VERIFICATION")
    print("=" * 60)
    print("Testing that main.py properly accepts both 'themethodbot' and 'budgetbot' arguments")
    print()
    
    success = test_argument_parser()
    
    print("\n📊 VERIFICATION RESULTS")
    print("=" * 50)
    
    if success:
        print("✅ Argument Parser: PASSED")
        print("\n🎉 MAIN.PY ARGUMENT PARSER SUCCESSFULLY UPDATED!")
        print("\n🚀 Usage:")
        print("   python main.py themethodbot    # Run The Method Bot")
        print("   python main.py budgetbot       # Run Budget Bot")
        print("   python main.py --help          # Show help with both options")
        print("\n📋 Both bots are now available as command line options!")
        return True
    else:
        print("❌ Argument Parser: FAILED")
        print("\n❌ SOME TESTS FAILED - PLEASE REVIEW")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

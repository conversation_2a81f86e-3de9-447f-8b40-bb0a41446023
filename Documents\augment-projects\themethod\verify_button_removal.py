#!/usr/bin/env python3
"""
Verification script for "Find Eligible Stores" button removal from OrderHelpButtons.

This script verifies that:
1. The "Find Eligible Stores" button has been completely removed
2. The "How to Order" URL button is preserved
3. The OrderHelpButtons class maintains proper Discord UI structure
4. Persistent functionality is maintained (timeout=None)
5. View registration is still working correctly
"""

import os
import re
import sys

def test_button_removal():
    """Test that the Find Eligible Stores button has been properly removed."""
    print("🔧 THEMETHODBOT ORDERHELPBUTTONS BUTTON REMOVAL VERIFICATION")
    print("=" * 70)
    print("Testing removal of 'Find Eligible Stores' button from OrderHelpButtons")
    print()
    
    # Test 1: Check OrderHelpButtons class structure
    print("🧪 Testing OrderHelpButtons Class Structure")
    print("=" * 50)
    
    try:
        with open('common/bot.py', 'r', encoding='utf-8') as f:
            bot_content = f.read()
    except FileNotFoundError:
        print("❌ CRITICAL: common/bot.py not found")
        return False
    
    # Test 1.1: Verify "Find Eligible Stores" button is completely removed from OrderHelpButtons
    print("\n📝 Test 1.1: Find Eligible Stores Button Removal from OrderHelpButtons")

    # Extract only the OrderHelpButtons class content for testing
    class_match = re.search(
        r'class OrderHelpButtons\(discord\.ui\.View\):(.*?)(?=\n\n|\nclass|\nasync def [^f]|\nif __name__|$)',
        bot_content,
        re.DOTALL
    )

    if not class_match:
        print("   ❌ FAILURE: Could not find OrderHelpButtons class")
        return False

    orderhelpbuttons_content = class_match.group(1)

    removed_patterns = [
        r'@discord\.ui\.button.*Find Eligible Stores',
        r'async def find_stores_button',
        r'Find Eligible Stores.*button clicked',
        r'title="🔎 Find Eligible Stores"',
        r'Follow these steps to find eligible restaurants',
        r'Step 1: Open Our Special Link',
        r'Step 2: Enter Your Address',
        r'Step 3: Open Link Again',
        r'Step 4: Browse Eligible Restaurants',
        r'tinyurl\.com/TheMethodUE'
    ]
    
    removal_success = True
    for pattern in removed_patterns:
        if re.search(pattern, orderhelpbuttons_content, re.IGNORECASE):
            print(f"   ❌ FOUND (should be removed): {pattern}")
            removal_success = False
        else:
            print(f"   ✅ REMOVED: {pattern}")
    
    if removal_success:
        print("   ✅ SUCCESS: All Find Eligible Stores button components removed")
    else:
        print("   ❌ FAILURE: Some Find Eligible Stores components still present")
        return False
    
    # Test 1.2: Verify "How to Order" URL button is preserved
    print("\n🔗 Test 1.2: How to Order URL Button Preservation")
    preserved_patterns = [
        r'class OrderHelpButtons\(discord\.ui\.View\):',
        r'super\(\).__init__\(timeout=None\)',
        r'self\.add_item\(discord\.ui\.Button\(',
        r'label="How to Order"',
        r'emoji="📋"',
        r'url=f"https://discord\.com/channels/.*1340210714891128882"'
    ]
    
    preservation_success = True
    for pattern in preserved_patterns:
        if re.search(pattern, bot_content):
            print(f"   ✅ PRESERVED: {pattern}")
        else:
            print(f"   ❌ MISSING: {pattern}")
            preservation_success = False
    
    if preservation_success:
        print("   ✅ SUCCESS: How to Order URL button properly preserved")
    else:
        print("   ❌ FAILURE: How to Order URL button missing components")
        return False
    
    # Test 1.3: Verify class structure integrity
    print("\n🏗️ Test 1.3: Class Structure Integrity")
    
    # Extract the OrderHelpButtons class
    class_match = re.search(
        r'class OrderHelpButtons\(discord\.ui\.View\):(.*?)(?=\nasync def [^f]|\nclass|\nif __name__|$)',
        bot_content,
        re.DOTALL
    )
    
    if not class_match:
        print("   ❌ FAILURE: Could not find OrderHelpButtons class")
        return False
    
    class_content = class_match.group(1)
    
    # Check that class only contains __init__ method and no button methods
    structure_checks = [
        (r'def __init__\(self\):', "✅ __init__ method present"),
        (r'super\(\).__init__\(timeout=None\)', "✅ Persistent timeout configuration"),
        (r'self\.add_item\(discord\.ui\.Button\(', "✅ URL button addition"),
        (r'async def.*button.*\(', "❌ No button methods should remain")
    ]
    
    structure_success = True
    for pattern, expected_result in structure_checks:
        if re.search(pattern, class_content):
            if expected_result.startswith("✅"):
                print(f"   {expected_result}")
            else:
                print(f"   ❌ FAILURE: Found button method (should be removed)")
                structure_success = False
        else:
            if expected_result.startswith("❌"):
                print(f"   ✅ SUCCESS: No button methods found")
            else:
                print(f"   ❌ MISSING: {expected_result}")
                structure_success = False
    
    if structure_success:
        print("   ✅ SUCCESS: Class structure is clean and proper")
    else:
        print("   ❌ FAILURE: Class structure has issues")
        return False
    
    # Test 2: Verify view registration is still working
    print("\n🧪 Testing View Registration Integration")
    print("=" * 50)
    
    try:
        with open('themethodbot/paymentapp.py', 'r', encoding='utf-8') as f:
            paymentapp_content = f.read()
    except FileNotFoundError:
        print("❌ CRITICAL: themethodbot/paymentapp.py not found")
        return False
    
    # Test 2.1: Check OrderHelpButtons is still registered
    print("\n📝 Test 2.1: OrderHelpButtons Registration")
    registration_patterns = [
        r'from common\.bot import OrderHelpButtons',
        r'order_help_buttons = OrderHelpButtons\(\)',
        r'bot\.add_view\(order_help_buttons\)'
    ]
    
    registration_success = True
    for pattern in registration_patterns:
        if re.search(pattern, paymentapp_content):
            print(f"   ✅ FOUND: {pattern}")
        else:
            print(f"   ❌ MISSING: {pattern}")
            registration_success = False
    
    if registration_success:
        print("   ✅ SUCCESS: OrderHelpButtons registration maintained")
    else:
        print("   ❌ FAILURE: OrderHelpButtons registration broken")
        return False
    
    # Test 3: Verify /open command integration
    print("\n🧪 Testing /open Command Integration")
    print("=" * 50)
    
    # Test 3.1: Check /open command still uses OrderHelpButtons
    print("\n📝 Test 3.1: /open Command Integration")
    open_command_patterns = [
        r'view = OrderHelpButtons\(\)',
        r'await.*\.send\(embed=embed, view=view\)',
        r'await.*\.edit\(embed=embed, view=view\)'
    ]
    
    integration_success = True
    for pattern in open_command_patterns:
        if re.search(pattern, bot_content):
            print(f"   ✅ FOUND: {pattern}")
        else:
            print(f"   ❌ MISSING: {pattern}")
            integration_success = False
    
    if integration_success:
        print("   ✅ SUCCESS: /open command integration maintained")
    else:
        print("   ❌ FAILURE: /open command integration broken")
        return False
    
    print("\n🎯 REMOVAL SUMMARY")
    print("=" * 50)
    print("✅ **Removed Components:**")
    print("   • @discord.ui.button decorator for Find Eligible Stores")
    print("   • find_stores_button async method")
    print("   • Complete embed creation logic with steps 1-4")
    print("   • All references to tinyurl.com/TheMethodUE")
    print("   • Button click logging and interaction handling")
    print()
    print("✅ **Preserved Components:**")
    print("   • OrderHelpButtons class structure")
    print("   • 'How to Order' URL button with proper emoji and styling")
    print("   • Persistent functionality (timeout=None)")
    print("   • View registration in setup_payment_views()")
    print("   • Integration with /open command")
    print()
    print("✅ **Maintained Functionality:**")
    print("   • /open command embed display")
    print("   • Persistent embed system")
    print("   • Discord UI structure integrity")
    print("   • Bot view registration system")
    
    print("\n📊 VERIFICATION RESULTS")
    print("=" * 50)
    print("✅ Button Removal: PASSED")
    print("✅ URL Button Preservation: PASSED") 
    print("✅ Class Structure: PASSED")
    print("✅ View Registration: PASSED")
    print("✅ Command Integration: PASSED")
    print()
    print("🎉 FIND ELIGIBLE STORES BUTTON SUCCESSFULLY REMOVED!")
    print()
    print("🚀 Expected Behavior:")
    print("   • /open command displays embed with only 'How to Order' button")
    print("   • 'Find Eligible Stores' button no longer appears")
    print("   • All other functionality remains unchanged")
    print("   • Persistent view functionality maintained")
    
    return True

if __name__ == "__main__":
    success = test_button_removal()
    sys.exit(0 if success else 1)

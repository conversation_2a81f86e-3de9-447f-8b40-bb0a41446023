#!/usr/bin/env python3
"""
Test script to verify BudgetBot updates:
1. Automatic order link detection
2. Removed commands (/check, /close, /open, /latestsummary)
3. New /ordersuccess command
4. Updated configuration
"""

import sys
import os
import re
import importlib.util

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_budgetbot_configuration():
    """Test that BudgetBot configuration has been updated correctly."""
    print("🧪 Testing BudgetBot Configuration Updates")
    print("=" * 50)
    
    try:
        from budgetbot.config import BUDGETBOT_COMMANDS, BUDGETBOT_FEATURES, BUDGETBOT_LINK_PATTERNS
        
        # Test command configuration
        expected_commands = {'ordersuccess', 'track'}
        actual_commands = set(BUDGETBOT_COMMANDS.keys())
        
        if actual_commands == expected_commands:
            print("   ✅ Command configuration updated correctly")
            print(f"      Available commands: {', '.join(actual_commands)}")
        else:
            print("   ❌ Command configuration incorrect")
            print(f"      Expected: {expected_commands}")
            print(f"      Actual: {actual_commands}")
            return False
        
        # Test feature flags
        required_features = ['auto_link_detection', 'order_success_tracking']
        for feature in required_features:
            if BUDGETBOT_FEATURES.get(feature, False):
                print(f"   ✅ Feature enabled: {feature}")
            else:
                print(f"   ❌ Feature missing or disabled: {feature}")
                return False
        
        # Test link patterns
        if BUDGETBOT_LINK_PATTERNS and len(BUDGETBOT_LINK_PATTERNS) > 0:
            print(f"   ✅ Link detection patterns configured ({len(BUDGETBOT_LINK_PATTERNS)} patterns)")
        else:
            print("   ❌ Link detection patterns missing")
            return False
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False

def test_budgetbot_implementation():
    """Test that BudgetBot implementation has the required changes."""
    print("\n🧪 Testing BudgetBot Implementation")
    print("=" * 50)
    
    try:
        with open('budgetbot/budgetbot.py', 'r', encoding='utf-8') as f:
            budgetbot_content = f.read()
    except FileNotFoundError:
        print("   ❌ budgetbot/budgetbot.py not found")
        return False
    
    # Test for automatic link detection
    detection_patterns = [
        r'detect_order_link',
        r'process_detected_order_link',
        r'async def on_message',
        r'BUDGETBOT_LINK_PATTERNS',
        r'auto_link_detection'
    ]
    
    detection_success = True
    for pattern in detection_patterns:
        if re.search(pattern, budgetbot_content):
            print(f"   ✅ Found: {pattern}")
        else:
            print(f"   ❌ Missing: {pattern}")
            detection_success = False
    
    # Test for removed commands
    removed_commands = ['name="check"', 'name="latestsummary"', 'name="open"', 'name="close"']
    removal_success = True
    for command in removed_commands:
        if command in budgetbot_content:
            print(f"   ❌ Command not removed: {command}")
            removal_success = False
        else:
            print(f"   ✅ Command removed: {command}")
    
    # Test for new ordersuccess command
    if 'name="ordersuccess"' in budgetbot_content:
        print("   ✅ New /ordersuccess command implemented")
    else:
        print("   ❌ /ordersuccess command missing")
        return False
    
    return detection_success and removal_success

def test_link_detection_function():
    """Test the link detection functionality."""
    print("\n🧪 Testing Link Detection Function")
    print("=" * 50)
    
    try:
        from budgetbot.budgetbot import detect_order_link
        from budgetbot.config import BUDGETBOT_FEATURES
        
        # Enable auto-detection for testing
        BUDGETBOT_FEATURES['auto_link_detection'] = True
        
        # Test cases
        test_cases = [
            ("https://www.ubereats.com/orders/group/abc123", True),
            ("Check out this order: https://ubereats.com/group/xyz789", True),
            ("ubereats.com/orders/group/test", True),
            ("Just some regular text", False),
            ("https://google.com", False),
            ("group order link here", False)  # This might match depending on patterns
        ]
        
        success = True
        for test_text, should_detect in test_cases:
            result = detect_order_link(test_text)
            detected = result is not None
            
            if detected == should_detect:
                status = "✅" if detected else "✅ (no link)"
                print(f"   {status} '{test_text[:50]}...' -> {'Detected' if detected else 'Not detected'}")
            else:
                print(f"   ❌ '{test_text[:50]}...' -> Expected: {'Detected' if should_detect else 'Not detected'}, Got: {'Detected' if detected else 'Not detected'}")
                success = False
        
        return success
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False

def test_embed_templates():
    """Test that embed templates have been updated."""
    print("\n🧪 Testing Embed Templates")
    print("=" * 50)
    
    try:
        from budgetbot.embed_templates import (
            create_budgetbot_order_summary_embed,
            create_budgetbot_success_embed,
            BUDGETBOT_PRIMARY_COLOR
        )
        
        print("   ✅ All required embed functions available")
        print("   ✅ BudgetBot primary color configured")
        
        # Test that we can create embeds
        test_result = {'location': {'address': 'Test Address'}, 'store_url': 'https://test.com'}
        test_items = ['Test Item 1', 'Test Item 2']
        test_fees = {'subtotal': 25.00, 'is_cad': False}
        
        embed = create_budgetbot_order_summary_embed(test_result, test_items, test_fees)
        if embed and hasattr(embed, 'title'):
            print("   ✅ Order summary embed creation works")
        else:
            print("   ❌ Order summary embed creation failed")
            return False
        
        success_embed = create_budgetbot_success_embed("Test Title", "Test Description")
        if success_embed and hasattr(success_embed, 'title'):
            print("   ✅ Success embed creation works")
        else:
            print("   ❌ Success embed creation failed")
            return False
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False

def main():
    """Run all BudgetBot update tests."""
    print("🔧 BUDGETBOT UPDATES VERIFICATION")
    print("=" * 70)
    print("Testing BudgetBot updates:")
    print("• Automatic order link detection")
    print("• Removed commands (/check, /close, /open, /latestsummary)")
    print("• New /ordersuccess command")
    print("• Updated configuration")
    print()
    
    # Run all tests
    tests = [
        ("Configuration Updates", test_budgetbot_configuration),
        ("Implementation Changes", test_budgetbot_implementation),
        ("Link Detection Function", test_link_detection_function),
        ("Embed Templates", test_embed_templates)
    ]
    
    results = []
    for test_name, test_func in tests:
        results.append((test_name, test_func()))
    
    # Summary
    print("\n📊 VERIFICATION RESULTS")
    print("=" * 50)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        emoji = "✅" if result else "❌"
        print(f"{emoji} {test_name}: {status}")
    
    if all(result for _, result in results):
        print("\n🎉 ALL BUDGETBOT UPDATES SUCCESSFULLY IMPLEMENTED!")
        print("\n🚀 Expected Functionality:")
        print("   • BudgetBot automatically detects Uber Eats links in messages")
        print("   • Responds with simplified order summaries (Location, Restaurant, Items, Subtotal)")
        print("   • /ordersuccess command for marking completed orders")
        print("   • /track command still available for order tracking")
        print("   • Removed /check, /latestsummary, /open, /close commands")
        print("   • Maintains budget-friendly orange branding")
        print("\n📋 Ready to test in Discord!")
        return True
    else:
        print("\n❌ SOME TESTS FAILED - PLEASE REVIEW")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

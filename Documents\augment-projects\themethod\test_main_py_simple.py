#!/usr/bin/env python3
"""
Simple test to verify main.py argument parser works for both bots.
"""

import subprocess
import sys

def test_main_py():
    """Test main.py argument parser functionality."""
    print("🧪 Testing main.py Argument Parser")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Help shows both bots
    print("\n📝 Test 1: Help Command")
    try:
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "themethodbot,budgetbot" in result.stdout:
            print("   ✅ Help command shows both bot options")
            tests_passed += 1
        else:
            print("   ❌ Help command doesn't show both bot options")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Invalid bot rejected
    print("\n📝 Test 2: Invalid Bot Rejection")
    try:
        result = subprocess.run([sys.executable, "main.py", "invalidbot"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode != 0 and "invalid choice" in result.stderr:
            print("   ✅ Invalid bot name properly rejected")
            tests_passed += 1
        else:
            print("   ❌ Invalid bot name not properly rejected")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Check that both modules exist
    print("\n📝 Test 3: Bot Modules Exist")
    try:
        import importlib.util
        
        themethodbot_spec = importlib.util.find_spec('themethodbot.themethodbot')
        budgetbot_spec = importlib.util.find_spec('budgetbot.budgetbot')
        
        if themethodbot_spec and budgetbot_spec:
            print("   ✅ Both bot modules can be imported")
            tests_passed += 1
        else:
            print("   ❌ One or both bot modules missing")
            if not themethodbot_spec:
                print("      - themethodbot.themethodbot not found")
            if not budgetbot_spec:
                print("      - budgetbot.budgetbot not found")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print(f"\n📊 Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("\n🎉 SUCCESS: main.py argument parser working correctly!")
        print("\n🚀 You can now run:")
        print("   python main.py themethodbot")
        print("   python main.py budgetbot")
        return True
    else:
        print("\n❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = test_main_py()
    sys.exit(0 if success else 1)

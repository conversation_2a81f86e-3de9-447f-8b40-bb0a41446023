import logging
from typing import Dict, Any

logger = logging.getLogger('generic_bot.fee_calculator')

def calculate_fees_without_delivery(fees_data: Dict[str, Any], subtotal: float, is_cad: bool = False) -> Dict[str, Any]:
    """Calculate basic fee-related values, excluding delivery fee."""
    # Use the provided subtotal parameter
    actual_subtotal = subtotal

    # Extract fees from fees_data
    service_fee = float(fees_data.get('service_fee', 0))
    ca_driver_benefit = float(fees_data.get('ca_driver_benefit', 0))
    taxes = float(fees_data.get('taxes', 0))
    uber_one_discount = float(fees_data.get('uber_one_discount', 0))

    # Calculate total fees (excluding delivery fee)
    total_fees = round(service_fee + ca_driver_benefit + taxes, 2)
    final_fees = round(total_fees - uber_one_discount, 2)

    # Calculate final total
    final_total = round(actual_subtotal + final_fees, 2)

    # Return calculated values
    return {
        'subtotal': actual_subtotal,
        'service_fee': service_fee,
        'ca_driver_benefit': ca_driver_benefit,
        'taxes': taxes,
        'uber_one_discount': uber_one_discount,
        'total_fees': total_fees,
        'final_fees': final_fees,
        'final_total': final_total,
        'is_cad': is_cad
    }

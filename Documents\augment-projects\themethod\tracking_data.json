{"4f299f07-1462-45fa-a1d6-a0c65c4c26ac": {"channel_id": 1389059792366211184, "start_time": 1751248826.2226086, "last_status": "EnrouteToEater", "order_link": "https://www.ubereats.com/orders/4f299f07-1462-45fa-a1d6-a0c65c4c26ac?entryPoint=checkout", "status_embed_message_id": 1389063013394415766, "status_history": [{"status": "Status: Preparing", "timestamp": 1751248826, "discord_timestamp": "<t:1751248826:t>"}, {"status": "Driver is heading to the store", "timestamp": 1751250576, "discord_timestamp": "<t:1751250576:t>"}, {"status": "Driver is on the way to you", "timestamp": 1751251035, "discord_timestamp": "<t:1751251035:t>"}], "delivery_ping_sent": false}, "e6fa9db1-6f6a-424e-ad19-5cae1e094fa9": {"channel_id": 1389061809583165550, "start_time": 1751249020.0240154, "last_status": "EnrouteToRestaurant", "order_link": "https://www.ubereats.com/orders/e6fa9db1-6f6a-424e-ad19-5cae1e094fa9?entryPoint=checkout", "status_embed_message_id": 1389063826615570534, "status_history": [{"status": "Status: Preparing", "timestamp": 1751249020, "discord_timestamp": "<t:1751249020:t>"}, {"status": "Driver is heading to the store", "timestamp": 1751251004, "discord_timestamp": "<t:1751251004:t>"}], "delivery_ping_sent": false}, "ebdef712-6f77-4b19-9d04-7a08c25a3d9c": {"channel_id": 1389060752832200745, "start_time": 1751249119.2768044, "last_status": "EnrouteToEater", "order_link": "https://www.ubereats.com/orders/ebdef712-6f77-4b19-9d04-7a08c25a3d9c?entryPoint=checkout", "status_embed_message_id": 1389064241948000316, "status_history": [{"status": "Status: Confirming", "timestamp": 1751249119, "discord_timestamp": "<t:1751249119:t>"}, {"status": "Status: Preparing", "timestamp": 1751249150, "discord_timestamp": "<t:1751249150:t>"}, {"status": "Driver is heading to the store", "timestamp": 1751249584, "discord_timestamp": "<t:1751249584:t>"}, {"status": "Driver is on the way to you", "timestamp": 1751250455, "discord_timestamp": "<t:1751250455:t>"}], "delivery_ping_sent": false}, "9c7cc206-ed23-445b-b700-5aa9d6c575e3": {"channel_id": 1389062857034956961, "start_time": 1751249647.2839692, "last_status": "TimeToMeetCourier", "order_link": "https://www.ubereats.com/orders/9c7cc206-ed23-445b-b700-5aa9d6c575e3?entryPoint=checkout", "status_embed_message_id": 1389066456104435855, "status_history": [{"status": "Driver is heading to the store", "timestamp": 1751249648, "discord_timestamp": "<t:1751249648:t>"}, {"status": "Driver is on the way to you", "timestamp": 1751250365, "discord_timestamp": "<t:1751250365:t>"}, {"status": "The driver is about to drop off your order!", "timestamp": 1751251129, "discord_timestamp": "<t:1751251129:t>"}], "delivery_ping_sent": true}, "https://www.ubereats.com/orders/a9f55746-8d16-4de2-9557-fc86208569a7?entryPoint=checkout": {"channel_id": 1389047071767527446, "start_time": 1751250306.05917, "last_status": null, "order_link": "https://www.ubereats.com/orders/https://www.ubereats.com/orders/a9f55746-8d16-4de2-9557-fc86208569a7?entryPoint=checkout", "status_embed_message_id": 1389069218233843907, "status_history": [], "delivery_ping_sent": false}}
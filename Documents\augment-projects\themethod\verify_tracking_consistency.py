#!/usr/bin/env python3
"""
Verification script for tracking data consistency across all tracking initialization points.

This script verifies that:
1. Both /ordersuccess and /track commands use the same persistent embed data structure
2. All required fields for persistent embeds are present
3. The data structure matches what the tracking system expects
4. All tracking initialization points are consistent

Run this script to verify the implementation is consistent across all tracking scenarios.
"""

import sys
import os
import re
import ast

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def extract_tracking_data_structure(file_path, pattern):
    """Extract tracking data structure from Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all matches of the pattern
        matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
        structures = []
        
        for match in matches:
            # Extract the dictionary structure
            dict_start = match.start()
            # Find the opening brace
            brace_start = content.find('{', dict_start)
            if brace_start == -1:
                continue
                
            # Find the matching closing brace
            brace_count = 0
            brace_end = brace_start
            for i, char in enumerate(content[brace_start:], brace_start):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        brace_end = i
                        break
            
            dict_content = content[brace_start:brace_end + 1]
            structures.append({
                'content': dict_content,
                'line_start': content[:brace_start].count('\n') + 1,
                'context': match.group(0)
            })
        
        return structures
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []

def parse_dict_keys(dict_content):
    """Extract keys from dictionary content."""
    try:
        # Simple regex to find dictionary keys
        key_pattern = r"'([^']+)':"
        keys = re.findall(key_pattern, dict_content)
        return set(keys)
    except Exception as e:
        print(f"Error parsing dictionary: {e}")
        return set()

def verify_tracking_consistency():
    """Verify tracking data consistency across all initialization points."""
    print("🔍 Verifying tracking data consistency...\n")
    
    # Expected fields for persistent embed system
    expected_fields = {
        'channel_id',
        'start_time', 
        'last_status',
        'order_link',
        'status_embed_message_id',
        'status_history',
        'delivery_ping_sent'
    }
    
    # Files to check
    files_to_check = [
        {
            'path': 'themethodbot/themethodbot.py',
            'patterns': [
                r'active_tracking\[order_id\]\s*=\s*\{[^}]+\}',
                r'active_tracking\[[^\]]+\]\s*=\s*\{[^}]+\}'
            ],
            'description': 'themethodbot.py tracking initialization'
        }
    ]
    
    all_structures = []
    
    for file_info in files_to_check:
        print(f"📁 Checking {file_info['description']}...")
        
        for pattern in file_info['patterns']:
            structures = extract_tracking_data_structure(file_info['path'], pattern)
            
            for i, structure in enumerate(structures):
                keys = parse_dict_keys(structure['content'])
                
                print(f"  📋 Structure {i+1} (line {structure['line_start']}):")
                print(f"     Keys found: {sorted(keys)}")
                
                # Check for missing fields
                missing_fields = expected_fields - keys
                extra_fields = keys - expected_fields
                
                if missing_fields:
                    print(f"     ❌ Missing fields: {sorted(missing_fields)}")
                else:
                    print(f"     ✅ All required fields present")
                
                if extra_fields:
                    print(f"     ℹ️  Extra fields: {sorted(extra_fields)}")
                
                all_structures.append({
                    'file': file_info['path'],
                    'line': structure['line_start'],
                    'keys': keys,
                    'content': structure['content']
                })
                
                print()
    
    # Compare all structures for consistency
    print("🔄 Comparing structures for consistency...")
    
    if len(all_structures) < 2:
        print("⚠️  Only found one tracking structure. Expected at least 2 (/track and /ordersuccess)")
        return False
    
    # Check if all structures have the same keys
    first_keys = all_structures[0]['keys']
    consistent = True
    
    for i, structure in enumerate(all_structures[1:], 1):
        if structure['keys'] != first_keys:
            print(f"❌ Structure {i+1} has different keys than structure 1")
            print(f"   Structure 1: {sorted(first_keys)}")
            print(f"   Structure {i+1}: {sorted(structure['keys'])}")
            consistent = False
    
    if consistent:
        print("✅ All tracking structures are consistent!")
        print(f"   Common fields: {sorted(first_keys)}")
    
    # Verify against expected fields
    print("\n🎯 Verifying against persistent embed requirements...")
    
    all_have_required = True
    for structure in all_structures:
        missing = expected_fields - structure['keys']
        if missing:
            print(f"❌ Structure at line {structure['line']} missing: {sorted(missing)}")
            all_have_required = False
    
    if all_have_required:
        print("✅ All structures have required persistent embed fields!")
    
    return consistent and all_have_required

def verify_save_load_functions():
    """Verify that save/load functions handle all the new fields."""
    print("\n💾 Verifying save/load function consistency...")
    
    try:
        # Check save_tracking_data function
        with open('themethodbot/themethodbot.py', 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Find save_tracking_data function
        save_func_match = re.search(r'async def save_tracking_data\(\):(.*?)(?=async def|\Z)', content, re.DOTALL)
        if save_func_match:
            save_func_content = save_func_match.group(1)
            
            # Check if it includes the new fields
            required_save_fields = [
                'status_embed_message_id',
                'status_history',
                'delivery_ping_sent'
            ]
            
            missing_in_save = []
            for field in required_save_fields:
                if field not in save_func_content:
                    missing_in_save.append(field)
            
            if missing_in_save:
                print(f"❌ save_tracking_data missing fields: {missing_in_save}")
                return False
            else:
                print("✅ save_tracking_data includes all new fields")
        
        # Find load_tracking_data function  
        load_func_match = re.search(r'async def load_tracking_data\(\):(.*?)(?=async def|\Z)', content, re.DOTALL)
        if load_func_match:
            load_func_content = load_func_match.group(1)
            
            # Check if it includes the new fields
            missing_in_load = []
            for field in required_save_fields:
                if field not in load_func_content:
                    missing_in_load.append(field)
            
            if missing_in_load:
                print(f"❌ load_tracking_data missing fields: {missing_in_load}")
                return False
            else:
                print("✅ load_tracking_data includes all new fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking save/load functions: {e}")
        return False

def main():
    """Run all verification checks."""
    print("🚀 Starting tracking consistency verification...\n")
    
    try:
        # Verify tracking data structure consistency
        structures_ok = verify_tracking_consistency()
        
        # Verify save/load function consistency
        save_load_ok = verify_save_load_functions()
        
        print("\n" + "="*60)
        print("📊 VERIFICATION SUMMARY")
        print("="*60)
        
        if structures_ok and save_load_ok:
            print("✅ ALL CHECKS PASSED!")
            print("\n🎯 Key Findings:")
            print("- Both /track and /ordersuccess commands use consistent data structure")
            print("- All required persistent embed fields are present")
            print("- Save/load functions handle all new fields")
            print("- Tracking initialization is consistent across all scenarios")
            print("\n🚀 The persistent embed system will work correctly for all tracking methods!")
            return 0
        else:
            print("❌ SOME CHECKS FAILED!")
            if not structures_ok:
                print("- Tracking data structures are inconsistent")
            if not save_load_ok:
                print("- Save/load functions need updates")
            return 1
            
    except Exception as e:
        print(f"❌ Verification failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())

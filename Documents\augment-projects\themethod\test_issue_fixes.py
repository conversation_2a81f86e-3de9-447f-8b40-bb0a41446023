#!/usr/bin/env python3
"""
Test script to verify the fixes for both issues:
1. Missing Status History Button in /track Command
2. TypeError in move_to_queue Function

This script tests the fixes without requiring a live Discord bot.
"""

import asyncio
import sys
import os
import unittest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import discord
from typing import Dict, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the modules we need to test
from common.bot import OrderStatusButtons, format_status_history, add_status_to_history

class TestIssueFixes(unittest.TestCase):
    """Test cases for the issue fixes."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.order_id = "test-order-123"
        self.active_tracking = {
            self.order_id: {
                'channel_id': 123456789,
                'start_time': 1751244336,
                'last_status': None,
                'order_link': f"https://www.ubereats.com/orders/{self.order_id}",
                'status_embed_message_id': None,
                'status_history': [],
                'delivery_ping_sent': False
            }
        }
    
    def test_order_status_buttons_creation(self):
        """Test that OrderStatusButtons view can be created properly."""
        print("🧪 Testing OrderStatusButtons creation...")
        
        # Create the view
        view = OrderStatusButtons(self.order_id, self.active_tracking)
        
        # Verify the view was created
        self.assertIsInstance(view, discord.ui.View)
        self.assertEqual(view.order_id, self.order_id)
        self.assertEqual(view.active_tracking_dict, self.active_tracking)
        
        # Verify the button exists
        buttons = [item for item in view.children if isinstance(item, discord.ui.Button)]
        self.assertEqual(len(buttons), 1)
        
        status_button = buttons[0]
        self.assertEqual(status_button.label, "📋 Status History")
        self.assertEqual(status_button.style, discord.ButtonStyle.secondary)
        
        print("✅ OrderStatusButtons creation test passed!")
    
    def test_status_history_functionality(self):
        """Test the status history management functions."""
        print("🧪 Testing status history functionality...")
        
        # Test adding status to history
        status_history = []
        timestamp = 1751244336
        
        # Add first status
        status_history = add_status_to_history(status_history, "Store is preparing your order", timestamp)
        self.assertEqual(len(status_history), 1)
        self.assertEqual(status_history[0]['status'], "Store is preparing your order")
        self.assertEqual(status_history[0]['timestamp'], timestamp)
        self.assertEqual(status_history[0]['discord_timestamp'], f"<t:{timestamp}:t>")
        
        # Add second status
        status_history = add_status_to_history(status_history, "Order is ready for pickup", timestamp + 300)
        self.assertEqual(len(status_history), 2)
        
        # Test formatting
        formatted = format_status_history(status_history)
        expected_lines = [
            f"<t:{timestamp}:t> Store is preparing your order",
            f"<t:{timestamp + 300}:t> Order is ready for pickup"
        ]
        self.assertEqual(formatted, "\n".join(expected_lines))
        
        print("✅ Status history functionality test passed!")

def test_move_to_queue_fix():
    """Test that move_to_queue function handles None position correctly."""
    print("🧪 Testing move_to_queue position validation fix...")
    
    try:
        with open('themethodbot/themethodbot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for position validation logic
        if 'if position is not None and not isinstance(position, int):' in content:
            print("✅ Found position parameter validation")
        else:
            print("❌ Position parameter validation not found")
            return False
        
        # Check for conditional position parameter passing
        if 'if position is not None:' in content and 'await channel.edit(category=queue_category, position=position)' in content:
            print("✅ Found conditional position parameter passing")
        else:
            print("❌ Conditional position parameter passing not found")
            return False
        
        # Check for fallback without position
        if 'await channel.edit(category=queue_category)' in content:
            print("✅ Found fallback channel.edit without position")
        else:
            print("❌ Fallback channel.edit without position not found")
            return False
        
        print("✅ move_to_queue fix verification passed")
        return True
        
    except Exception as e:
        print(f"❌ Error reading themethodbot.py: {e}")
        return False

def test_track_command_fix():
    """Test that /track command creates initial embed with OrderStatusButtons."""
    print("🧪 Testing /track command Status History button fix...")
    
    try:
        with open('themethodbot/themethodbot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for OrderStatusButtons import
        if 'from common.bot import OrderStatusButtons' in content:
            print("✅ Found OrderStatusButtons import")
        else:
            print("❌ OrderStatusButtons import not found")
            return False
        
        # Check for initial embed creation
        if 'initial_embed = discord.Embed(' in content:
            print("✅ Found initial embed creation")
        else:
            print("❌ Initial embed creation not found")
            return False
        
        # Check for view creation
        if 'view = OrderStatusButtons(order_id, active_tracking)' in content:
            print("✅ Found OrderStatusButtons view creation")
        else:
            print("❌ OrderStatusButtons view creation not found")
            return False
        
        # Check for embed sent with view
        if 'await interaction.channel.send(embed=initial_embed, view=view)' in content:
            print("✅ Found embed sent with view")
        else:
            print("❌ Embed sent with view not found")
            return False
        
        print("✅ /track command fix verification passed")
        return True
        
    except Exception as e:
        print(f"❌ Error reading themethodbot.py: {e}")
        return False

def test_persistent_embed_view_integration():
    """Test that persistent embed system includes OrderStatusButtons view."""
    print("🧪 Testing persistent embed OrderStatusButtons integration...")
    
    try:
        with open('common/bot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for view creation in persistent embed logic
        if 'view = OrderStatusButtons(order_id, active_tracking_dict)' in content:
            print("✅ Found OrderStatusButtons view creation in persistent embed logic")
        else:
            print("❌ OrderStatusButtons view creation in persistent embed logic not found")
            return False
        
        # Check for message.edit with view
        if 'await message.edit(embed=embed, view=view)' in content:
            print("✅ Found message.edit with view parameter")
        else:
            print("❌ message.edit with view parameter not found")
            return False
        
        # Check for channel.send with view
        if 'await channel.send(embed=embed, view=view)' in content:
            print("✅ Found channel.send with view parameter")
        else:
            print("❌ channel.send with view parameter not found")
            return False
        
        print("✅ Persistent embed view integration verification passed")
        return True
        
    except Exception as e:
        print(f"❌ Error reading common/bot.py: {e}")
        return False

async def run_async_tests():
    """Run the async tests."""
    test_instance = TestIssueFixes()
    test_instance.setUp()
    
    # Run the unit tests
    test_instance.test_order_status_buttons_creation()
    test_instance.test_status_history_functionality()

def main():
    """Main test function."""
    print("🚀 Starting Issue Fixes Test Suite")
    print("=" * 50)
    
    # Track test results
    results = []
    
    try:
        # Run async tests first
        asyncio.run(run_async_tests())
        
        # Run file-based verification tests
        results.append(test_move_to_queue_fix())
        results.append(test_track_command_fix())
        results.append(test_persistent_embed_view_integration())
        
        print("=" * 50)
        passed = sum(results)
        total = len(results)
        
        if passed == total:
            print("✅ All tests passed! Both issues have been fixed:")
            print("   1. ✅ OrderStatusButtons view created successfully")
            print("   2. ✅ Status History functionality working correctly")
            print("   3. ✅ move_to_queue handles None position parameter")
            print("   4. ✅ /track command creates initial embed with Status History button")
            print("   5. ✅ Persistent embed system includes OrderStatusButtons view")
            print("\n🎯 Expected Behavior After Deployment:")
            print("   • /track command will show Status History button")
            print("   • move_to_queue will not crash with None position")
            print("   • Status History button will show timestamped order updates")
            print("   • Persistent embeds will update in-place with buttons")
            return True
        else:
            print(f"❌ {total - passed} test(s) failed!")
            return False
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

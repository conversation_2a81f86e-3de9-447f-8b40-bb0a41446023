#!/usr/bin/env python3
"""
Test script to verify that payment commands are no longer ephemeral.
This script checks the code to ensure ephemeral=True has been removed from payment functions.
"""

import os

def check_payment_functions():
    """Check payment functions for ephemeral=True usage"""

    print("🔍 Checking payment functions for ephemeral settings...")
    print("=" * 60)

    all_good = True

    # Check specific payment functions in common/bot.py
    print(f"\n📁 Checking common/bot.py payment functions...")

    if not os.path.exists('common/bot.py'):
        print(f"❌ File not found: common/bot.py")
        return False

    with open('common/bot.py', 'r', encoding='utf-8') as f:
        content = f.read()

    # Check specific payment functions
    payment_functions = {
        'cerv': 'async def cerv(interaction: discord.Interaction):',
        'nelo': 'async def nelo(interaction: discord.Interaction):',
        'Glitchyz': 'async def Glitchyz(interaction: discord.Interaction):',
        'dessie': 'async def dessie(interaction: discord.Interaction):',
        'payment_methods_callback': 'async def payment_methods_callback(interaction: discord.Interaction):'
    }

    for func_name, func_signature in payment_functions.items():
        if func_signature in content:
            # Find the function content
            func_start = content.find(func_signature)
            if func_start != -1:
                # Find the end of this function (next async def, class definition, or end of file)
                next_func_start = content.find('\nasync def ', func_start + 1)
                next_class_start = content.find('\nclass ', func_start + 1)

                # Use the closest boundary
                boundaries = [pos for pos in [next_func_start, next_class_start] if pos != -1]
                if boundaries:
                    func_end = min(boundaries)
                    func_content = content[func_start:func_end]
                else:
                    func_content = content[func_start:]

                # Check if this function uses ephemeral=True
                if 'ephemeral=True' in func_content:
                    print(f"❌ Function {func_name} still uses ephemeral=True")
                    # Debug: show where ephemeral=True was found
                    lines_with_ephemeral = [line.strip() for line in func_content.split('\n') if 'ephemeral=True' in line]
                    for line in lines_with_ephemeral:
                        print(f"   Found: {line}")
                    all_good = False
                else:
                    print(f"✅ Function {func_name} does not use ephemeral=True")
        else:
            print(f"⚠️ Function {func_name} not found")

    # Check PaymentMethodSelector in themethodbot/paymentapp.py
    print(f"\n📁 Checking themethodbot/paymentapp.py...")

    if os.path.exists('themethodbot/paymentapp.py'):
        with open('themethodbot/paymentapp.py', 'r', encoding='utf-8') as f:
            paymentapp_content = f.read()

        # Check the payment_methods button in PaymentMethodSelector
        if 'async def payment_methods(self, interaction: discord.Interaction' in paymentapp_content:
            # Find the payment_methods function
            func_start = paymentapp_content.find('async def payment_methods(self, interaction: discord.Interaction')
            if func_start != -1:
                # Find the end of this function
                next_func_start = paymentapp_content.find('\n    @discord.ui.button', func_start + 1)
                if next_func_start == -1:
                    next_func_start = paymentapp_content.find('\ndef ', func_start + 1)
                if next_func_start == -1:
                    next_func_start = len(paymentapp_content)

                func_content = paymentapp_content[func_start:next_func_start]

                # Check for ephemeral=True in the send_message call (not error handling)
                send_message_lines = [line for line in func_content.split('\n') if 'await interaction.response.send_message' in line and 'embed=embed' in line]

                ephemeral_in_main_response = any('ephemeral=True' in line for line in send_message_lines)

                if ephemeral_in_main_response:
                    print(f"❌ PaymentMethodSelector payment_methods button still uses ephemeral=True")
                    all_good = False
                else:
                    print(f"✅ PaymentMethodSelector payment_methods button does not use ephemeral=True")
        else:
            print(f"⚠️ PaymentMethodSelector payment_methods function not found")
    else:
        print(f"❌ File not found: themethodbot/paymentapp.py")
        all_good = False

    return all_good

def check_import_statements():
    """Check that payment functions are properly imported"""
    
    print(f"\n🔍 Checking import statements...")
    print("-" * 40)
    
    # Check themethodbot.py imports
    themethodbot_file = 'themethodbot/themethodbot.py'
    if os.path.exists(themethodbot_file):
        with open(themethodbot_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if payment functions are imported from common.bot
        if 'from common.bot import' in content and any(func in content for func in ['cerv', 'nelo', 'Glitchyz']):
            print("✅ Payment functions properly imported in themethodbot.py")
        else:
            print("⚠️ Payment function imports may need verification in themethodbot.py")
    
    # Check paymentapp.py imports
    paymentapp_file = 'themethodbot/paymentapp.py'
    if os.path.exists(paymentapp_file):
        with open(paymentapp_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'from common.bot import cerv, nelo, Glitchyz' in content:
            print("✅ Payment functions properly imported in paymentapp.py")
        else:
            print("⚠️ Payment function imports may need verification in paymentapp.py")

def check_slash_commands():
    """Check that slash commands are properly defined"""
    
    print(f"\n🔍 Checking slash command definitions...")
    print("-" * 40)
    
    themethodbot_file = 'themethodbot/themethodbot.py'
    if os.path.exists(themethodbot_file):
        with open(themethodbot_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for slash command definitions
        commands = ['cerv_command', 'nelo_command', 'glitchyz_command']
        
        for cmd in commands:
            if f'async def {cmd}' in content:
                print(f"✅ Slash command {cmd} found")
            else:
                print(f"⚠️ Slash command {cmd} not found")

def main():
    """Main test function"""
    print("🚀 Testing Payment Command Visibility Changes...")
    print("This script verifies that payment commands are no longer ephemeral.\n")
    
    # Test 1: Check payment functions
    functions_ok = check_payment_functions()
    
    # Test 2: Check imports
    check_import_statements()
    
    # Test 3: Check slash commands
    check_slash_commands()
    
    print("\n" + "=" * 60)
    print("📋 Summary:")
    print("Payment commands that should now be PUBLIC (visible to everyone):")
    print("• /cerv - Cerv's payment methods")
    print("• /nelo - Nelo's payment methods") 
    print("• /glitchyz - Glitchyz's payment methods")
    print("• Payment Methods button - Shows payment method selector")
    print("• Individual payment buttons - Shows specific payment details")
    
    if functions_ok:
        print("\n🎯 Result:")
        print("✅ All payment functions have been updated to be PUBLIC")
        print("✅ Payment commands will now be visible to everyone in the channel")
        print("✅ Users can see payment information without it being hidden")
        
        print("\n💡 What this means:")
        print("• When someone uses /cerv, everyone in the channel can see the payment info")
        print("• When someone clicks the Payment Methods button, everyone can see it")
        print("• Payment information is now transparent and visible to all users")
        print("• This makes it easier for customers to see payment options")
    else:
        print("\n❌ Some issues were found:")
        print("• Some payment functions may still be using ephemeral=True")
        print("• Please review the issues above and fix them")
    
    return functions_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

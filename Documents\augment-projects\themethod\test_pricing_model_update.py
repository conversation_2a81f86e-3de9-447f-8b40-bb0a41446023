#!/usr/bin/env python3
"""
Test script to verify the updated pricing model changes:
1. Discount amount changed from $20 to $25 (CAD: $26 to $32.50)
2. Method Fee changed from $7.50 to $10.00 (CAD: $9.75 to $13.00)
3. Minimum cart subtotal changed from $25 to $20 (CAD unchanged at $30)
4. Overflow threshold changed from $25 to $20 (CAD unchanged at $30)
"""

import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_pricing_model_updates():
    """Test all pricing model updates."""
    print("🔧 THEMETHODBOT PRICING MODEL UPDATE VERIFICATION")
    print("=" * 70)
    print("Testing updated pricing model with:")
    print("• Discount: $20 → $25 (CAD: $26 → $32.50)")
    print("• Method Fee: $7.50 → $10.00 (CAD: $9.75 → $13.00)")
    print("• Minimum Subtotal: $25 → $20 (CAD unchanged: $30)")
    print("• Overflow Threshold: $25 → $20 (CAD unchanged: $30)")
    print()
    
    # Test 1: Fee calculation functions
    print("🧪 Testing Fee Calculation Functions")
    print("=" * 50)
    
    try:
        from common.bot import calculate_fees
        from themethodbot.fee_calculator import calculate_fees_without_delivery
        
        # Test USD calculations
        print("\n📝 Test 1.1: USD Fee Calculations")
        test_fees_data = {
            'service_fee': 5.00,
            'delivery_fee': 2.99,
            'ca_driver_benefit': 0.00,
            'taxes': 3.50,
            'uber_one_discount': 2.00
        }
        
        result_usd = calculate_fees(test_fees_data, 30.00, is_cad=False)
        
        expected_discount = 25.00
        expected_method_fee = 10.00
        expected_threshold = 20.00
        
        print(f"   Subtotal: ${result_usd['subtotal']:.2f}")
        print(f"   Fixed Discount: ${result_usd.get('fixed_discount', 'N/A'):.2f} (Expected: ${expected_discount:.2f})")
        print(f"   Method Fee: ${result_usd.get('method_fee', 'N/A'):.2f} (Expected: ${expected_method_fee:.2f})")
        print(f"   Overflow Fee: ${result_usd.get('overflow_fee', 'N/A'):.2f} (Expected: ${30.00 - expected_threshold:.2f})")
        print(f"   Discounted Subtotal: ${result_usd.get('discounted_subtotal', 'N/A'):.2f}")
        
        # Verify USD values
        usd_success = True
        if abs(result_usd.get('fixed_discount', 0) - expected_discount) > 0.01:
            print(f"   ❌ USD Discount incorrect: got ${result_usd.get('fixed_discount', 0):.2f}, expected ${expected_discount:.2f}")
            usd_success = False
        if abs(result_usd.get('method_fee', 0) - expected_method_fee) > 0.01:
            print(f"   ❌ USD Method Fee incorrect: got ${result_usd.get('method_fee', 0):.2f}, expected ${expected_method_fee:.2f}")
            usd_success = False
        if abs(result_usd.get('overflow_fee', 0) - (30.00 - expected_threshold)) > 0.01:
            print(f"   ❌ USD Overflow Fee incorrect: got ${result_usd.get('overflow_fee', 0):.2f}, expected ${30.00 - expected_threshold:.2f}")
            usd_success = False
            
        if usd_success:
            print("   ✅ USD calculations correct")
        
        # Test CAD calculations
        print("\n📝 Test 1.2: CAD Fee Calculations")
        result_cad = calculate_fees(test_fees_data, 35.00, is_cad=True)
        
        expected_discount_cad = 32.50
        expected_method_fee_cad = 13.00
        expected_threshold_cad = 30.00
        
        print(f"   Subtotal: CA${result_cad['subtotal']:.2f}")
        print(f"   Fixed Discount: CA${result_cad.get('fixed_discount', 'N/A'):.2f} (Expected: CA${expected_discount_cad:.2f})")
        print(f"   Method Fee: CA${result_cad.get('method_fee', 'N/A'):.2f} (Expected: CA${expected_method_fee_cad:.2f})")
        print(f"   Overflow Fee: CA${result_cad.get('overflow_fee', 'N/A'):.2f} (Expected: CA${35.00 - expected_threshold_cad:.2f})")
        print(f"   Discounted Subtotal: CA${result_cad.get('discounted_subtotal', 'N/A'):.2f}")
        
        # Verify CAD values
        cad_success = True
        if abs(result_cad.get('fixed_discount', 0) - expected_discount_cad) > 0.01:
            print(f"   ❌ CAD Discount incorrect: got CA${result_cad.get('fixed_discount', 0):.2f}, expected CA${expected_discount_cad:.2f}")
            cad_success = False
        if abs(result_cad.get('method_fee', 0) - expected_method_fee_cad) > 0.01:
            print(f"   ❌ CAD Method Fee incorrect: got CA${result_cad.get('method_fee', 0):.2f}, expected CA${expected_method_fee_cad:.2f}")
            cad_success = False
        if abs(result_cad.get('overflow_fee', 0) - (35.00 - expected_threshold_cad)) > 0.01:
            print(f"   ❌ CAD Overflow Fee incorrect: got CA${result_cad.get('overflow_fee', 0):.2f}, expected CA${35.00 - expected_threshold_cad:.2f}")
            cad_success = False
            
        if cad_success:
            print("   ✅ CAD calculations correct")
            
        if usd_success and cad_success:
            print("   ✅ SUCCESS: Fee calculation functions updated correctly")
        else:
            print("   ❌ FAILURE: Fee calculation functions have issues")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: Failed to test fee calculations: {str(e)}")
        return False
    
    # Test 2: Minimum order validation
    print("\n🧪 Testing Minimum Order Validation")
    print("=" * 50)
    
    try:
        from common.bot import check_order_limits
        from themethodbot.embed_templates import check_order_limits as embed_check_order_limits
        
        # Test USD minimum order validation
        print("\n📝 Test 2.1: USD Minimum Order Validation")
        
        # Test below minimum ($19.99 should fail)
        result_below = check_order_limits(19.99, is_cad=False)
        if result_below is not None:
            print("   ✅ $19.99 correctly rejected (below $20 minimum)")
        else:
            print("   ❌ $19.99 incorrectly accepted (should be below $20 minimum)")
            return False
            
        # Test at minimum ($20.00 should pass)
        result_at_min = check_order_limits(20.00, is_cad=False)
        if result_at_min is None:
            print("   ✅ $20.00 correctly accepted (at $20 minimum)")
        else:
            print("   ❌ $20.00 incorrectly rejected (should be at $20 minimum)")
            return False
            
        # Test CAD minimum order validation (unchanged at $30)
        print("\n📝 Test 2.2: CAD Minimum Order Validation")
        
        # Test below minimum (CA$29.99 should fail)
        result_cad_below = check_order_limits(29.99, is_cad=True)
        if result_cad_below is not None:
            print("   ✅ CA$29.99 correctly rejected (below CA$30 minimum)")
        else:
            print("   ❌ CA$29.99 incorrectly accepted (should be below CA$30 minimum)")
            return False
            
        # Test at minimum (CA$30.00 should pass)
        result_cad_at_min = check_order_limits(30.00, is_cad=True)
        if result_cad_at_min is None:
            print("   ✅ CA$30.00 correctly accepted (at CA$30 minimum)")
        else:
            print("   ❌ CA$30.00 incorrectly rejected (should be at CA$30 minimum)")
            return False
            
        print("   ✅ SUCCESS: Minimum order validation updated correctly")
        
    except Exception as e:
        print(f"   ❌ ERROR: Failed to test minimum order validation: {str(e)}")
        return False
    
    # Test 3: Constants in themethodbot.py
    print("\n🧪 Testing Constants in themethodbot.py")
    print("=" * 50)
    
    try:
        from themethodbot.themethodbot import MIN_SUBTOTAL, MAX_SUBTOTAL
        
        print(f"\n📝 Test 3.1: Subtotal Constants")
        print(f"   MIN_SUBTOTAL: ${MIN_SUBTOTAL:.1f} (Expected: $20.0)")
        print(f"   MAX_SUBTOTAL: ${MAX_SUBTOTAL:.1f} (Expected: $35.0)")
        
        if abs(MIN_SUBTOTAL - 20.0) < 0.01:
            print("   ✅ MIN_SUBTOTAL correctly updated to $20.0")
        else:
            print(f"   ❌ MIN_SUBTOTAL incorrect: got ${MIN_SUBTOTAL:.1f}, expected $20.0")
            return False
            
        print("   ✅ SUCCESS: Constants updated correctly")
        
    except Exception as e:
        print(f"   ❌ ERROR: Failed to test constants: {str(e)}")
        return False
    
    # Test 4: Sample pricing calculation
    print("\n🧪 Testing Sample Pricing Calculation")
    print("=" * 50)
    
    try:
        print("\n📝 Test 4.1: Complete Pricing Example")
        
        # Sample order: $30 subtotal, $3 delivery, $4 service fee, $2.50 taxes
        sample_fees = {
            'service_fee': 4.00,
            'delivery_fee': 3.00,
            'ca_driver_benefit': 0.00,
            'taxes': 2.50,
            'uber_one_discount': 0.00
        }
        
        result = calculate_fees(sample_fees, 30.00, is_cad=False)
        
        print(f"   Original Subtotal: ${result['subtotal']:.2f}")
        print(f"   Discount (-$25): -${result.get('fixed_discount', 25.00):.2f}")
        print(f"   Discounted Subtotal: ${result['discounted_subtotal']:.2f}")
        print(f"   Overflow Fee: ${result['overflow_fee']:.2f}")
        print(f"   Method Fee: ${result['method_fee']:.2f}")
        print(f"   Other Fees: ${result['final_fees'] - result['method_fee']:.2f}")
        print(f"   Final Total: ${result['final_total']:.2f}")
        
        # Expected calculation:
        # Original: $30.00
        # Discount: -$25.00 → Discounted: $5.00
        # Overflow: $30.00 - $20.00 = $10.00
        # Method Fee: $10.00
        # Other Fees: $4.00 + $2.50 = $6.50 (service + taxes, delivery excluded from final_fees)
        # Final Total: $5.00 + $10.00 + $10.00 + $6.50 = $31.50
        
        expected_discounted = 5.00
        expected_overflow = 10.00
        expected_method_fee = 10.00
        
        if (abs(result['discounted_subtotal'] - expected_discounted) < 0.01 and
            abs(result['overflow_fee'] - expected_overflow) < 0.01 and
            abs(result['method_fee'] - expected_method_fee) < 0.01):
            print("   ✅ Sample calculation correct")
        else:
            print("   ❌ Sample calculation incorrect")
            return False
            
    except Exception as e:
        print(f"   ❌ ERROR: Failed to test sample calculation: {str(e)}")
        return False
    
    print("\n📊 VERIFICATION SUMMARY")
    print("=" * 50)
    print("✅ **Updated Values:**")
    print("   • USD Discount: $20.00 → $25.00")
    print("   • CAD Discount: $26.00 → $32.50")
    print("   • USD Method Fee: $7.50 → $10.00")
    print("   • CAD Method Fee: $9.75 → $13.00")
    print("   • USD Minimum Subtotal: $25.00 → $20.00")
    print("   • USD Overflow Threshold: $25.00 → $20.00")
    print("   • CAD values unchanged (minimum: $30.00, threshold: $30.00)")
    print()
    print("✅ **Files Updated:**")
    print("   • common/bot.py - calculate_fees() function")
    print("   • common/bot.py - check_order_limits() function")
    print("   • common/bot.py - overflow thresholds")
    print("   • themethodbot/fee_calculator.py - calculate_fees_without_delivery()")
    print("   • themethodbot/embed_templates.py - pricing display")
    print("   • themethodbot/themethodbot.py - constants and hardcoded values")
    print()
    print("🎉 ALL PRICING MODEL UPDATES VERIFIED SUCCESSFULLY!")
    print()
    print("🚀 Expected Behavior:")
    print("   • Orders with $20+ subtotal now accepted (was $25+)")
    print("   • $25 discount applied instead of $20")
    print("   • $10 Method Fee instead of $7.50")
    print("   • Overflow fee starts at $20 instead of $25")
    print("   • All embeds display new pricing values")
    
    return True

if __name__ == "__main__":
    success = test_pricing_model_updates()
    sys.exit(0 if success else 1)

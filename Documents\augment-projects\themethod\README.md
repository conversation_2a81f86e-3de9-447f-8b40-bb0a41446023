# The Method Discord Bot

This repository contains The Method Discord bot used for order management and processing.

## Project Structure

The project is organized as follows:

- `themethodbot/` - The main Discord bot for order management
- `common/` - Shared code used by the bot

## Running the Bot

You can run The Method Bot using the main.py script:

```bash
# Run The Method Bot
python main.py themethodbot
```

Alternatively, you can run the bot directly from its folder:

```bash
# Run The Method Bot
cd themethodbot
python main.py
```

Or use the run_all_bots script (which now only runs themethodbot):

```bash
python run_all_bots.py
```

Or simply double-click the `run_all_bots.bat` file (Windows) or run `./run_all_bots.sh` (Linux/Mac).

## Dependencies

The bot dependencies are listed in the requirements.txt file.

## Configuration

The bot uses environment variables for configuration. Make sure to set up the appropriate .env file with the required variables.

## Common Code

The `common/` directory contains shared code used by the bot:

- `bot.py` - Common bot functionality
- `check_group_order.py` - Functions for checking Uber Eats group orders
- `extract_label.py` - Functions for extracting labels from orders
- `config.py` - Shared configuration

#!/usr/bin/env python3
"""
Verification script to confirm TOKEN_2 has been updated correctly in all environment files.
"""

import os

def verify_token_replacement():
    """Verify that TOKEN_2 has been updated in all environment files"""
    
    print("🔍 Verifying TOKEN_2 replacement...")
    print("=" * 50)
    
    # Expected new token value
    expected_token = "NTcyOTkxOTcxMzU5MTk1MTM4.GPM6W3.Rik0PhmLkXcvGc1Onep4g3gh6Mde46KDxE1cCs"
    old_token = "NTcyOTkxOTcxMzU5MTk1MTM4.GgLaUh.O6_9iKwFJN0-jnttrMGNABt0WjuezpCuYVrHgo"
    
    # Files to check
    env_files = [
        '.env',
        'themethodbot/.env', 
        'env'
    ]
    
    all_passed = True
    
    for env_file in env_files:
        print(f"\n📁 Checking {env_file}...")
        
        if not os.path.exists(env_file):
            print(f"❌ File not found: {env_file}")
            all_passed = False
            continue
            
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if new token is present
        if expected_token in content:
            print(f"✅ New TOKEN_2 found in {env_file}")
        else:
            print(f"❌ New TOKEN_2 NOT found in {env_file}")
            all_passed = False
        
        # Check if old token is still present (should not be)
        if old_token in content:
            print(f"❌ Old TOKEN_2 still present in {env_file}")
            all_passed = False
        else:
            print(f"✅ Old TOKEN_2 successfully removed from {env_file}")
        
        # Extract TOKEN_2 line for verification
        lines = content.split('\n')
        token_line = None
        for line in lines:
            if line.startswith('TOKEN_2='):
                token_line = line
                break
        
        if token_line:
            print(f"📋 Current TOKEN_2 line: {token_line}")
            if expected_token in token_line:
                print(f"✅ TOKEN_2 line contains correct new token")
            else:
                print(f"❌ TOKEN_2 line does not contain correct new token")
                all_passed = False
        else:
            print(f"⚠️ No TOKEN_2 line found in {env_file}")
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All TOKEN_2 replacements verified successfully!")
        print("✅ Close button and clear delivered functionality will use the new token.")
        print("✅ Old token has been completely replaced in all files.")
    else:
        print("⚠️ Some verification checks failed.")
        print("Please review the results above.")
    
    return all_passed

def check_token_usage():
    """Check where TOKEN_2 is used in the codebase"""
    print("\n🔍 Checking TOKEN_2 usage in codebase...")
    print("-" * 40)
    
    # Files that use TOKEN_2
    usage_files = [
        'themethodbot/themethodbot.py',
        'themethodbot/common/bot.py',
        'common/bot.py'
    ]
    
    for file_path in usage_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'TOKEN_2' in content:
                print(f"✅ {file_path}: Uses TOKEN_2")
                
                # Count occurrences
                token_count = content.count('TOKEN_2')
                print(f"   📊 TOKEN_2 referenced {token_count} times")
                
                # Check for specific usage patterns
                if 'headers = {"Authorization": TOKEN_2' in content:
                    print(f"   🔑 Used for Discord API authorization headers")
                if 'if not TOKEN_2:' in content:
                    print(f"   ✔️ Has proper TOKEN_2 validation checks")
            else:
                print(f"ℹ️ {file_path}: Does not use TOKEN_2")
        else:
            print(f"⚠️ {file_path}: File not found")

def main():
    """Main verification function"""
    print("🚀 Starting TOKEN_2 replacement verification...")
    
    # Verify token replacement
    token_success = verify_token_replacement()
    
    # Check token usage
    check_token_usage()
    
    print("\n📋 Summary:")
    print("TOKEN_2 is used for:")
    print("• /cleardelivered command - Closes channels with 'delivered' in name")
    print("• /cleardelivering command - Closes channels with 'delivering' in name") 
    print("• Close button functionality - Sends $close and $transcript commands")
    print("• Discord API authentication for automated channel management")
    
    if token_success:
        print("\n🎯 Result:")
        print("✅ TOKEN_2 has been successfully updated to the new authentication token.")
        print("✅ Close button and clear delivered functionality will now use the new token.")
        print("✅ All environment files have been updated consistently.")
    else:
        print("\n❌ Some issues were found. Please review the verification results.")
    
    return token_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

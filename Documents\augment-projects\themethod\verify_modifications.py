#!/usr/bin/env python3
"""
Verification script for the two specific modifications:
1. Remove promo information from /open command
2. Create credits system logging channel functionality
"""

import asyncio
import sys
import os
import re

def test_promo_removal():
    """Test that promo information has been removed from /open command."""
    print("🧪 Testing Promo Information Removal from /open Command")
    print("=" * 60)
    
    # Read the common/bot.py file
    try:
        with open("common/bot.py", "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ FAILURE: common/bot.py file not found")
        return False
    
    # Test 1: Check that the promo field has been removed
    print("\n📝 Test 1: Promo Field Removal")
    promo_patterns = [
        r'Current Promo',
        r'\$20 OFF.*Minimum \$25 subtotal',
        r'embed\.add_field\(\s*name="<:moneybag:.*Current Promo"'
    ]
    
    promo_found = False
    for pattern in promo_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            promo_found = True
            print(f"   ❌ Found promo pattern: {pattern}")
            break
    
    if not promo_found:
        print("   ✅ SUCCESS: No promo information found in /open command")
    else:
        print("   ❌ FAILURE: Promo information still present")
        return False
    
    # Test 2: Check that open_store function still exists
    print("\n🔍 Test 2: Open Store Function Integrity")
    if "async def open_store(interaction: discord.Interaction):" in content:
        print("   ✅ SUCCESS: open_store function still exists")
    else:
        print("   ❌ FAILURE: open_store function not found")
        return False
    
    # Test 3: Check that embed creation is still intact
    print("\n📋 Test 3: Embed Creation Integrity")
    embed_patterns = [
        r'embed = discord\.Embed\(',
        r'title=".*The Method is Now Open.*"',
        r'embed\.set_image\(',
        r'embed\.set_footer\('
    ]
    
    embed_checks = []
    for pattern in embed_patterns:
        if re.search(pattern, content):
            embed_checks.append(True)
            print(f"   ✅ Found: {pattern}")
        else:
            embed_checks.append(False)
            print(f"   ❌ Missing: {pattern}")
    
    if all(embed_checks):
        print("   ✅ SUCCESS: Embed creation structure intact")
        return True
    else:
        print("   ❌ FAILURE: Embed creation structure damaged")
        return False

def test_credits_logging():
    """Test that credits system logging has been implemented."""
    print("\n🧪 Testing Credits System Logging Implementation")
    print("=" * 60)
    
    # Read the themethodbot.py file
    try:
        with open("themethodbot/themethodbot.py", "r", encoding="utf-8") as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ FAILURE: themethodbot/themethodbot.py file not found")
        return False
    
    # Test 1: Check for logging channel ID constant
    print("\n📝 Test 1: Logging Channel ID Constant")
    if "CREDITS_LOG_CHANNEL_ID = 1389728392601010300" in content:
        print("   ✅ SUCCESS: Credits logging channel ID defined")
    else:
        print("   ❌ FAILURE: Credits logging channel ID not found")
        return False
    
    # Test 2: Check for log_credits_event function
    print("\n🔧 Test 2: Log Credits Event Function")
    if "async def log_credits_event(" in content:
        print("   ✅ SUCCESS: log_credits_event function found")
    else:
        print("   ❌ FAILURE: log_credits_event function not found")
        return False
    
    # Test 3: Check for logging in redeemcredits command
    print("\n💳 Test 3: Redeemcredits Command Logging")
    redeemcredits_patterns = [
        r'await log_credits_event\("redemption"',
        r'await log_credits_event\("error".*interaction\.user'
    ]
    
    redeemcredits_checks = []
    for pattern in redeemcredits_patterns:
        if re.search(pattern, content):
            redeemcredits_checks.append(True)
            print(f"   ✅ Found: {pattern}")
        else:
            redeemcredits_checks.append(False)
            print(f"   ❌ Missing: {pattern}")
    
    if all(redeemcredits_checks):
        print("   ✅ SUCCESS: Redeemcredits logging implemented")
    else:
        print("   ❌ FAILURE: Redeemcredits logging incomplete")
        return False
    
    # Test 4: Check for logging in creditcheck command
    print("\n🔍 Test 4: Creditcheck Command Logging")
    creditcheck_patterns = [
        r'await log_credits_event\("balance_check"',
        r'admin=interaction\.user'
    ]
    
    creditcheck_checks = []
    for pattern in creditcheck_patterns:
        if re.search(pattern, content):
            creditcheck_checks.append(True)
            print(f"   ✅ Found: {pattern}")
        else:
            creditcheck_checks.append(False)
            print(f"   ❌ Missing: {pattern}")
    
    if all(creditcheck_checks):
        print("   ✅ SUCCESS: Creditcheck logging implemented")
    else:
        print("   ❌ FAILURE: Creditcheck logging incomplete")
        return False
    
    # Test 5: Check for logging in removecredits command
    print("\n🗑️ Test 5: Removecredits Command Logging")
    removecredits_patterns = [
        r'await log_credits_event\("credit_removal"',
        r'amount=amount.*balance=new_balance'
    ]
    
    removecredits_checks = []
    for pattern in removecredits_patterns:
        if re.search(pattern, content):
            removecredits_checks.append(True)
            print(f"   ✅ Found: {pattern}")
        else:
            removecredits_checks.append(False)
            print(f"   ❌ Missing: {pattern}")
    
    if all(removecredits_checks):
        print("   ✅ SUCCESS: Removecredits logging implemented")
    else:
        print("   ❌ FAILURE: Removecredits logging incomplete")
        return False
    
    # Test 6: Check for embed types in logging function
    print("\n📊 Test 6: Logging Embed Types")
    embed_types = [
        r'event_type == "redemption"',
        r'event_type == "balance_check"',
        r'event_type == "credit_removal"',
        r'event_type == "error"'
    ]
    
    embed_type_checks = []
    for event_type in embed_types:
        if re.search(event_type, content):
            embed_type_checks.append(True)
            print(f"   ✅ Found: {event_type}")
        else:
            embed_type_checks.append(False)
            print(f"   ❌ Missing: {event_type}")
    
    if all(embed_type_checks):
        print("   ✅ SUCCESS: All logging embed types implemented")
        return True
    else:
        print("   ❌ FAILURE: Some logging embed types missing")
        return False

def show_implementation_summary():
    """Show a summary of the implemented changes."""
    print("\n🎯 IMPLEMENTATION SUMMARY")
    print("=" * 60)
    
    print("\n✅ **Modification 1: Remove Promo Information from /open Command**")
    print("   • Removed the 'Current Promo' field from the /open command embed")
    print("   • Removed the '$20 OFF your order! (Minimum $25 subtotal)' text")
    print("   • Preserved all other /open command functionality")
    print("   • Maintained embed structure, image, footer, and buttons")
    
    print("\n✅ **Modification 2: Create Credits System Logging Channel**")
    print("   • Added CREDITS_LOG_CHANNEL_ID constant: 1389728392601010300")
    print("   • Implemented log_credits_event() function with embed formatting")
    print("   • Added logging to /redeemcredits command (redemption + error events)")
    print("   • Added logging to /creditcheck command (balance_check + error events)")
    print("   • Added logging to /removecredits command (credit_removal + error events)")
    print("   • Implemented 4 event types: redemption, balance_check, credit_removal, error")
    print("   • Used color-coded embeds: Green (redemption), Blue (balance_check), Red (removal), Orange (error)")
    print("   • Added timestamps and structured logging for easy monitoring")

def main():
    """Run all verification tests."""
    print("🔧 THEMETHODBOT MODIFICATIONS VERIFICATION")
    print("=" * 60)
    print("Testing two specific modifications:")
    print("1. Remove promo information from /open command")
    print("2. Create credits system logging channel functionality")
    
    # Test promo removal
    promo_test_passed = test_promo_removal()
    
    # Test credits logging
    credits_test_passed = test_credits_logging()
    
    # Show implementation summary
    show_implementation_summary()
    
    # Final results
    print(f"\n📊 VERIFICATION RESULTS")
    print("=" * 60)
    
    if promo_test_passed:
        print("✅ Promo Removal: PASSED")
    else:
        print("❌ Promo Removal: FAILED")
    
    if credits_test_passed:
        print("✅ Credits Logging: PASSED")
    else:
        print("❌ Credits Logging: FAILED")
    
    if promo_test_passed and credits_test_passed:
        print("\n🎉 ALL MODIFICATIONS SUCCESSFULLY IMPLEMENTED!")
        print("\n🚀 Next Steps:")
        print("   1. Restart themethodbot to load the changes")
        print("   2. Test /open command to verify promo removal")
        print("   3. Test credits commands to verify logging functionality")
        print("   4. Check Discord channel 1389728392601010300 for log messages")
        return True
    else:
        print("\n❌ SOME MODIFICATIONS FAILED - PLEASE REVIEW")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
